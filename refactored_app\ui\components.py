"""
UI components for the AI Friend application.
Reusable Gradio components and styling.
"""

import gradio as gr
from typing import Any, Callable, Dict, List, Optional, Tuple

from ..config import config


def create_theme() -> gr.Theme:
    """Create custom theme for the application."""
    return gr.Theme(
        primary_hue=config.ui.theme_primary_hue,
        secondary_hue=config.ui.theme_secondary_hue,
        neutral_hue="slate",
        spacing_size="md",
        radius_size="md"
    )


def create_login_form() -> Tuple[gr.Column, Dict[str, gr.Component]]:
    """
    Create login form components.
    
    Returns:
        Tuple of (container, components_dict)
    """
    with gr.<PERSON>umn(visible=True) as login_container:
        gr.Markdown("# 🤗 Welcome to AI Friend")
        gr.Markdown("Please log in or register to continue")
        
        with gr.Tab("Login"):
            login_username = gr.Textbox(
                label="Username",
                placeholder="Enter your username",
                max_lines=1
            )
            login_password = gr.Textbox(
                label="Password",
                placeholder="Enter your password",
                type="password",
                max_lines=1
            )
            login_button = gr.<PERSON><PERSON>("Login", variant="primary")
            login_message = gr.Markdown("")
        
        with gr.Tab("Register"):
            register_username = gr.Textbox(
                label="Username",
                placeholder="Choose a username (3-30 characters)",
                max_lines=1
            )
            register_name = gr.Textbox(
                label="Display Name",
                placeholder="How should I call you?",
                max_lines=1
            )
            register_password = gr.Textbox(
                label="Password",
                placeholder="Choose a secure password (8+ characters)",
                type="password",
                max_lines=1
            )
            register_button = gr.Button("Register", variant="primary")
            register_message = gr.Markdown("")
    
    components = {
        'login_username': login_username,
        'login_password': login_password,
        'login_button': login_button,
        'login_message': login_message,
        'register_username': register_username,
        'register_name': register_name,
        'register_password': register_password,
        'register_button': register_button,
        'register_message': register_message
    }
    
    return login_container, components


def create_chat_interface() -> Tuple[gr.Column, Dict[str, gr.Component]]:
    """
    Create main chat interface components.
    
    Returns:
        Tuple of (container, components_dict)
    """
    with gr.Column(visible=False) as chat_container:
        # Header
        with gr.Row():
            gr.Markdown("# 💬 Chat with Mandy")
            logout_button = gr.Button("Logout", size="sm", variant="secondary")
        
        # Chat display
        chatbot = gr.Chatbot(
            label="Conversation",
            height=500,
            show_label=False,
            container=True,
            bubble_full_width=False,
            avatar_images=("👤", "🤗")
        )
        
        # Message input
        with gr.Row():
            message_input = gr.Textbox(
                label="Message",
                placeholder="Type your message here...",
                scale=4,
                max_lines=3,
                show_label=False
            )
            send_button = gr.Button("Send", scale=1, variant="primary")
        
        # Feedback section
        with gr.Accordion("Feedback", open=False):
            gr.Markdown("Help me improve by rating my responses:")
            
            with gr.Row():
                feedback_type = gr.Dropdown(
                    choices=["general", "humor", "empathy", "memory", "helpfulness"],
                    value="general",
                    label="Feedback Type",
                    scale=2
                )
                
                with gr.Column(scale=1):
                    thumbs_up = gr.Button("👍", size="sm")
                    thumbs_down = gr.Button("👎", size="sm")
            
            feedback_comment = gr.Textbox(
                label="Additional Comments (Optional)",
                placeholder="Any specific feedback?",
                max_lines=2
            )
            
            feedback_message = gr.Markdown("")
    
    components = {
        'chatbot': chatbot,
        'message_input': message_input,
        'send_button': send_button,
        'logout_button': logout_button,
        'feedback_type': feedback_type,
        'thumbs_up': thumbs_up,
        'thumbs_down': thumbs_down,
        'feedback_comment': feedback_comment,
        'feedback_message': feedback_message
    }
    
    return chat_container, components


def create_state_components() -> Dict[str, gr.State]:
    """
    Create state management components.
    
    Returns:
        Dictionary of state components
    """
    return {
        'user_id': gr.State(""),
        'user_name': gr.State("Friend"),
        'chat_history': gr.State([]),
        'is_logged_in': gr.State(False)
    }


def create_error_display() -> gr.Markdown:
    """Create error message display component."""
    return gr.Markdown("", visible=False, elem_classes=["error-message"])


def create_loading_indicator() -> gr.HTML:
    """Create loading indicator component."""
    return gr.HTML(
        """
        <div class="loading-indicator" style="display: none;">
            <div class="spinner"></div>
            <span>Thinking...</span>
        </div>
        """,
        visible=False
    )


def create_custom_css() -> str:
    """Create custom CSS for the application."""
    return """
    /* Custom styling for AI Friend */
    .gradio-container {
        max-width: 800px !important;
        margin: 0 auto;
    }
    
    .error-message {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin: 0.5rem 0;
    }
    
    .success-message {
        color: #155724;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin: 0.5rem 0;
    }
    
    .loading-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        color: #6c757d;
    }
    
    .spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 0.5rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Chat styling */
    .chatbot .message {
        margin: 0.5rem 0;
    }
    
    .chatbot .message.user {
        background-color: #e3f2fd;
        margin-left: 2rem;
    }
    
    .chatbot .message.bot {
        background-color: #f5f5f5;
        margin-right: 2rem;
    }
    
    /* Feedback buttons */
    .feedback-buttons {
        display: flex;
        gap: 0.5rem;
        margin: 0.5rem 0;
    }
    
    .feedback-buttons button {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid #dee2e6;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .feedback-buttons button:hover {
        background-color: #f8f9fa;
        border-color: #adb5bd;
    }
    
    .feedback-buttons button.positive:hover {
        background-color: #d4edda;
        border-color: #28a745;
        color: #28a745;
    }
    
    .feedback-buttons button.negative:hover {
        background-color: #f8d7da;
        border-color: #dc3545;
        color: #dc3545;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .gradio-container {
            max-width: 100% !important;
            margin: 0;
            padding: 0.5rem;
        }
        
        .chatbot {
            height: 400px !important;
        }
    }
    """


def format_error_message(message: str) -> str:
    """Format error message with proper styling."""
    return f'<div class="error-message">❌ {message}</div>'


def format_success_message(message: str) -> str:
    """Format success message with proper styling."""
    return f'<div class="success-message">✅ {message}</div>'


def format_info_message(message: str) -> str:
    """Format info message with proper styling."""
    return f'<div class="info-message">ℹ️ {message}</div>'


def validate_input_length(text: str, max_length: int = 1000) -> Tuple[bool, str]:
    """
    Validate input text length.
    
    Args:
        text: Input text to validate
        max_length: Maximum allowed length
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not text or not text.strip():
        return False, "Message cannot be empty"
    
    if len(text) > max_length:
        return False, f"Message too long (max {max_length} characters)"
    
    return True, ""


def sanitize_chat_history(history: List[List[str]], max_messages: int = 50) -> List[List[str]]:
    """
    Sanitize and limit chat history.
    
    Args:
        history: Chat history list
        max_messages: Maximum number of messages to keep
        
    Returns:
        Sanitized chat history
    """
    if not isinstance(history, list):
        return []
    
    # Limit number of messages
    if len(history) > max_messages:
        history = history[-max_messages:]
    
    # Sanitize each message
    sanitized = []
    for msg in history:
        if isinstance(msg, list) and len(msg) >= 2:
            user_msg = str(msg[0])[:1000] if msg[0] else ""
            bot_msg = str(msg[1])[:1000] if msg[1] else ""
            sanitized.append([user_msg, bot_msg])
    
    return sanitized
