# AI Friend - Refactored Version

A clean, maintainable AI chatbot application built with proper software engineering principles.

## 🏗️ Architecture Overview

This is a complete refactoring of the original 9,733-line monolithic application into a modular, production-ready system.

### Key Improvements

- ✅ **Modular Architecture**: Separated into logical packages
- ✅ **Proper Error Handling**: Consistent exception handling throughout
- ✅ **Configuration Management**: Centralized, validated configuration
- ✅ **Logging**: Structured logging with rotation
- ✅ **Database Design**: Clean models and efficient queries
- ✅ **Memory Management**: Simplified embedding system
- ✅ **Security**: Proper input validation and sanitization
- ✅ **Testing Ready**: Dependency injection for easy testing
- ✅ **Production Ready**: Health checks, graceful shutdown

## 📁 Project Structure

```
refactored_app/
├── config/           # Configuration management
│   ├── __init__.py
│   └── settings.py   # Centralized configuration
├── core/             # Core utilities and exceptions
│   ├── __init__.py
│   ├── exceptions.py # Custom exception classes
│   ├── logging.py    # Logging configuration
│   └── utils.py      # Common utilities
├── database/         # Database layer
│   ├── __init__.py
│   ├── manager.py    # Database operations
│   └── models.py     # Data models and schema
├── auth/             # Authentication system
│   ├── __init__.py
│   └── manager.py    # User auth and management
├── memory/           # Memory and embedding system
│   ├── __init__.py
│   ├── embeddings.py # Embedding management
│   └── manager.py    # Memory operations
├── llm/              # Language model client
│   ├── __init__.py
│   └── client.py     # Gemini API client
├── chat/             # Chat management
│   ├── __init__.py
│   └── manager.py    # Conversation handling
├── ui/               # User interface
│   ├── __init__.py
│   ├── app.py        # Main Gradio app
│   ├── components.py # UI components
│   └── handlers.py   # Event handlers
├── main.py           # Application entry point
├── requirements.txt  # Dependencies
└── README.md         # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Google Gemini API key

### Installation

1. **Clone and setup**:
   ```bash
   cd refactored_app
   pip install -r requirements.txt
   ```

2. **Configure environment**:
   ```bash
   # Create .env file
   echo "GEMINI_API_KEY=your_api_key_here" > .env
   ```

3. **Run the application**:
   ```bash
   python -m refactored_app.main
   ```

   Or with custom options:
   ```bash
   python -m refactored_app.main --host 0.0.0.0 --port 7861 --debug
   ```

## 🔧 Configuration

Configuration is managed through the `config/settings.py` file with environment variable support:

```python
# Environment variables
GEMINI_API_KEY=your_api_key
SERVER_NAME=0.0.0.0
SERVER_PORT=7861
GRADIO_SHARE=false
```

## 🏛️ Architecture Details

### Database Layer
- **Clean Models**: Proper data classes with validation
- **Connection Management**: Simplified SQLite handling
- **Schema Versioning**: Proper migration support
- **Indexing**: Optimized queries with appropriate indices

### Memory System
- **Embedding Management**: Singleton pattern for model loading
- **Vector Operations**: Efficient similarity search
- **Memory Consolidation**: Automatic cleanup and promotion
- **Persistence**: Reliable storage with SQLite

### Authentication
- **Secure Passwords**: bcrypt hashing
- **Session Management**: Proper user session handling
- **Input Validation**: Comprehensive validation and sanitization

### Chat Management
- **Context Building**: Smart context assembly for LLM
- **Streaming Responses**: Real-time response generation
- **Memory Integration**: Automatic memory creation and recall
- **Feedback System**: User feedback collection and processing

### UI Layer
- **Component Separation**: Reusable UI components
- **Event Handling**: Clean separation of UI logic
- **State Management**: Proper state handling
- **Error Handling**: User-friendly error messages

## 🔍 Key Features

### Memory System
- Embedding-based memory retrieval
- Automatic importance scoring
- Memory consolidation and cleanup
- Emotional context preservation

### Conversation Management
- Context-aware responses
- Streaming response generation
- User profile adaptation
- Feedback integration

### Security
- Input sanitization
- SQL injection prevention
- Rate limiting ready
- Secure password storage

## 🧪 Testing

The modular architecture makes testing straightforward:

```python
# Example test structure
def test_memory_creation():
    db_manager = DatabaseManager(":memory:")
    memory_manager = MemoryManager(db_manager)
    
    memory_id = memory_manager.create_memory(
        user_id="test_user",
        text="Test memory",
        importance=0.8
    )
    
    assert memory_id is not None
```

## 📊 Performance

### Optimizations
- **Database**: Proper indexing and query optimization
- **Memory**: LRU caching for embeddings
- **UI**: Efficient state management
- **LLM**: Streaming responses for better UX

### Monitoring
- Structured logging with metrics
- Health check endpoints
- Performance tracking ready

## 🔒 Security

### Implemented
- ✅ Password hashing with bcrypt
- ✅ Input validation and sanitization
- ✅ SQL injection prevention
- ✅ Error message sanitization

### Production Recommendations
- Add rate limiting
- Implement HTTPS
- Add CSRF protection
- Set up monitoring and alerting

## 🚀 Deployment

### Development
```bash
python -m refactored_app.main --debug
```

### Production
```bash
python -m refactored_app.main --host 0.0.0.0 --port 8080
```

### Docker (Recommended)
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "-m", "refactored_app.main"]
```

## 📈 Monitoring

The application includes structured logging and health checks:

- **Logs**: Automatic rotation with structured format
- **Metrics**: Performance and usage tracking
- **Health**: Database and service health monitoring

## 🤝 Contributing

The clean architecture makes contributions easier:

1. **Add Features**: Create new modules in appropriate packages
2. **Fix Bugs**: Clear separation makes debugging easier
3. **Testing**: Dependency injection enables comprehensive testing
4. **Documentation**: Each module is self-documenting

## 📝 License

This refactored version maintains the same functionality as the original while providing a maintainable, production-ready codebase.

---

**Original Issues Addressed:**
- ❌ 9,733-line monolith → ✅ Modular architecture
- ❌ No error handling → ✅ Comprehensive error management
- ❌ Global state → ✅ Proper dependency injection
- ❌ No testing → ✅ Testable architecture
- ❌ Security issues → ✅ Secure implementation
- ❌ Performance problems → ✅ Optimized operations
