"""
Relationship Manager for tracking relationship depth and stages.
Manages the evolution of the relationship between user and AI friend.
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta

try:
    from ..config import config
    from ..core import LoggerMixin, get_current_timestamp, time_since_timestamp
    from ..database import DatabaseManager
except Exception:
    # Fallback when running as a top-level app (no package context)
    from config import config
    from core import LoggerMixin, get_current_timestamp, time_since_timestamp
    from database import DatabaseManager


class RelationshipManager(LoggerMixin):
    """
    Manages relationship depth and stages between user and AI friend.
    Tracks relationship evolution based on interactions and vulnerability.
    """
    
    # Relationship stages and their depth ranges
    STAGES = {
        (0, 10): "acquaintance",
        (10, 30): "casual friend", 
        (30, 60): "friend",
        (60, 101): "close friend"  # Include 100+
    }
    
    # Parameters for each relationship stage
    STAGE_PARAMETERS = {
        "acquaintance": {
            "humor_level": 3,
            "empathy_level": 0.5,
            "warmth_level": 0.6,
            "self_disclosure": 0.2,
            "casual_language": False,
            "personal_anecdotes": False
        },
        "casual friend": {
            "humor_level": 5,
            "empathy_level": 0.7,
            "warmth_level": 0.7,
            "self_disclosure": 0.4,
            "casual_language": True,
            "personal_anecdotes": False
        },
        "friend": {
            "humor_level": 7,
            "empathy_level": 0.8,
            "warmth_level": 0.8,
            "self_disclosure": 0.6,
            "casual_language": True,
            "personal_anecdotes": True
        },
        "close friend": {
            "humor_level": 9,
            "empathy_level": 0.9,
            "warmth_level": 0.9,
            "self_disclosure": 0.8,
            "casual_language": True,
            "personal_anecdotes": True
        }
    }
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize relationship manager."""
        self.db_manager = db_manager
        
        # Relationship progression factors
        self.vulnerability_factor = config.relationship.vulnerability_factor
        self.interaction_factor = config.relationship.interaction_factor
        self.reciprocity_factor = config.relationship.reciprocity_factor
        self.frequency_factor = config.relationship.frequency_factor
        self.consistency_factor = config.relationship.consistency_factor
        
        # Decay parameters
        self.decay_rate_per_day = 0.1  # Relationship depth decay per day of inactivity
        self.max_decay_days = 30  # Maximum days to apply decay
        
        self.logger.info("Relationship manager initialized")
    
    def update_relationship_depth(self, user_id: str, interaction_data: Dict[str, Any]) -> float:
        """
        Update relationship depth based on interaction.
        
        Args:
            user_id: User identifier
            interaction_data: Data about the interaction including:
                - vulnerability_score: How much user opened up (0.0-1.0)
                - message_length: Length of user's message
                - reciprocity_signal: Whether user showed interest in Mandy
                - conversation_duration: Duration of conversation
                - emotional_intensity: Intensity of emotions shared
                
        Returns:
            New relationship depth score
        """
        try:
            # Get current relationship depth
            current_depth = self._get_current_depth(user_id)
            
            # Extract interaction parameters
            vulnerability_score = interaction_data.get('vulnerability_score', 0.0)
            message_length = interaction_data.get('message_length', 0)
            reciprocity_signal = interaction_data.get('reciprocity_signal', False)
            emotional_intensity = interaction_data.get('emotional_intensity', 0.0)
            
            # Calculate depth increases
            vulnerability_increase = vulnerability_score * self.vulnerability_factor
            
            # Interaction length factor (normalized)
            interaction_increase = min(0.05, (message_length / 100) * self.interaction_factor)
            
            # Reciprocity bonus
            reciprocity_increase = self.reciprocity_factor if reciprocity_signal else 0.0
            
            # Emotional intensity bonus
            emotion_increase = emotional_intensity * 0.1
            
            # Total increase
            total_increase = (vulnerability_increase + interaction_increase + 
                            reciprocity_increase + emotion_increase)
            
            # Apply decay based on time since last interaction
            decay = self._calculate_relationship_decay(user_id)
            
            # Calculate new depth
            new_depth = max(0.0, min(100.0, current_depth + total_increase - decay))
            
            # Save updated depth
            self._save_relationship_depth(user_id, new_depth)
            
            # Log the update
            self.logger.info(
                f"Updated relationship depth for user {user_id}: "
                f"{current_depth:.2f} -> {new_depth:.2f} "
                f"(Inc: {total_increase:.3f} [Vuln:{vulnerability_increase:.3f}, "
                f"Len:{interaction_increase:.3f}, Reci:{reciprocity_increase:.3f}], "
                f"Decay: {decay:.3f})"
            )
            
            return new_depth
            
        except Exception as e:
            self.logger.error(f"Error updating relationship depth: {e}")
            return self._get_current_depth(user_id)
    
    def _get_current_depth(self, user_id: str) -> float:
        """Get current relationship depth from database."""
        try:
            query = f"""
                SELECT relationship_depth, last_interaction_time
                FROM {config.database.user_table}
                WHERE user_id = ?
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result and result[0]['relationship_depth'] is not None:
                return float(result[0]['relationship_depth'])
            
            # Default depth for new users
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error getting current relationship depth: {e}")
            return 0.0
    
    def _save_relationship_depth(self, user_id: str, depth: float) -> None:
        """Save relationship depth to database."""
        try:
            current_time = get_current_timestamp()
            
            query = f"""
                UPDATE {config.database.user_table}
                SET relationship_depth = ?, last_interaction_time = ?
                WHERE user_id = ?
            """
            
            self.db_manager.execute_update(query, (depth, current_time, user_id))
            
        except Exception as e:
            self.logger.error(f"Error saving relationship depth: {e}")
    
    def _calculate_relationship_decay(self, user_id: str) -> float:
        """Calculate relationship decay based on time since last interaction."""
        try:
            query = f"""
                SELECT last_interaction_time
                FROM {config.database.user_table}
                WHERE user_id = ?
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if not result or not result[0]['last_interaction_time']:
                return 0.0  # No decay for first interaction
            
            last_interaction = result[0]['last_interaction_time']
            days_since_last = time_since_timestamp(last_interaction) / (24 * 3600)
            
            # Apply decay only if more than 1 day has passed
            if days_since_last > 1:
                decay_days = min(days_since_last, self.max_decay_days)
                decay = decay_days * self.decay_rate_per_day
                return decay
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating relationship decay: {e}")
            return 0.0
    
    def get_relationship_stage_and_depth(self, user_id: str) -> Tuple[str, float]:
        """Get current relationship stage and depth."""
        try:
            depth = self._get_current_depth(user_id)
            stage = self._depth_to_stage(depth)
            return stage, depth
            
        except Exception as e:
            self.logger.error(f"Error getting relationship stage and depth: {e}")
            return "acquaintance", 0.0
    
    def _depth_to_stage(self, depth: float) -> str:
        """Convert relationship depth to stage name."""
        for (min_depth, max_depth), stage in self.STAGES.items():
            if min_depth <= depth < max_depth:
                return stage
        return "acquaintance"  # Default fallback
    
    def get_stage_parameters(self, user_id: str) -> Dict[str, Any]:
        """Get parameters for current relationship stage."""
        try:
            stage, depth = self.get_relationship_stage_and_depth(user_id)
            base_params = self.STAGE_PARAMETERS.get(stage, self.STAGE_PARAMETERS["acquaintance"])
            
            # Add depth-based fine-tuning within stage
            params = base_params.copy()
            
            # Fine-tune parameters based on exact depth within stage
            stage_ranges = {name: range_tuple for range_tuple, name in self.STAGES.items()}
            if stage in stage_ranges:
                min_depth, max_depth = stage_ranges[stage]
                stage_progress = (depth - min_depth) / (max_depth - min_depth)
                
                # Gradually increase parameters within stage
                if 'humor_level' in params:
                    params['humor_level'] = min(10, params['humor_level'] + int(stage_progress * 2))
                if 'empathy_level' in params:
                    params['empathy_level'] = min(1.0, params['empathy_level'] + stage_progress * 0.1)
                if 'warmth_level' in params:
                    params['warmth_level'] = min(1.0, params['warmth_level'] + stage_progress * 0.1)
            
            return params
            
        except Exception as e:
            self.logger.error(f"Error getting stage parameters: {e}")
            return self.STAGE_PARAMETERS["acquaintance"]
    
    @staticmethod
    def get_stage_parameters_static(stage_name: str) -> Dict[str, Any]:
        """Static method to get stage parameters without needing an instance."""
        return RelationshipManager.STAGE_PARAMETERS.get(
            stage_name, 
            RelationshipManager.STAGE_PARAMETERS["acquaintance"]
        )
    
    def get_relationship_summary(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive relationship summary."""
        try:
            stage, depth = self.get_relationship_stage_and_depth(user_id)
            parameters = self.get_stage_parameters(user_id)
            
            # Get interaction history
            query = f"""
                SELECT COUNT(*) as total_interactions,
                       AVG(vulnerability_score) as avg_vulnerability,
                       MAX(last_interaction_time) as last_interaction
                FROM {config.database.memory_table}
                WHERE user_id = ?
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            interaction_stats = {}
            if result:
                interaction_stats = {
                    'total_interactions': result[0]['total_interactions'] or 0,
                    'avg_vulnerability': result[0]['avg_vulnerability'] or 0.0,
                    'last_interaction': result[0]['last_interaction']
                }
            
            return {
                'user_id': user_id,
                'current_stage': stage,
                'relationship_depth': depth,
                'stage_parameters': parameters,
                'interaction_stats': interaction_stats,
                'next_stage': self._get_next_stage(stage),
                'depth_to_next_stage': self._get_depth_to_next_stage(depth)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting relationship summary: {e}")
            return {}
    
    def _get_next_stage(self, current_stage: str) -> Optional[str]:
        """Get the next relationship stage."""
        stages_order = ["acquaintance", "casual friend", "friend", "close friend"]
        
        try:
            current_index = stages_order.index(current_stage)
            if current_index < len(stages_order) - 1:
                return stages_order[current_index + 1]
            return None  # Already at highest stage
            
        except ValueError:
            return "casual friend"  # Default next stage
    
    def _get_depth_to_next_stage(self, current_depth: float) -> Optional[float]:
        """Get depth points needed to reach next stage."""
        for (min_depth, max_depth), stage in self.STAGES.items():
            if min_depth <= current_depth < max_depth:
                return max_depth - current_depth
        return None  # Already at highest stage
    
    def reset_relationship(self, user_id: str) -> None:
        """Reset relationship depth to beginning."""
        try:
            self._save_relationship_depth(user_id, 0.0)
            self.logger.info(f"Reset relationship for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error resetting relationship: {e}")
    
    def boost_relationship(self, user_id: str, boost_amount: float) -> float:
        """Manually boost relationship depth (for special events)."""
        try:
            current_depth = self._get_current_depth(user_id)
            new_depth = min(100.0, current_depth + boost_amount)
            self._save_relationship_depth(user_id, new_depth)
            
            self.logger.info(f"Boosted relationship for user {user_id}: {current_depth:.2f} -> {new_depth:.2f}")
            return new_depth
            
        except Exception as e:
            self.logger.error(f"Error boosting relationship: {e}")
            return self._get_current_depth(user_id)
