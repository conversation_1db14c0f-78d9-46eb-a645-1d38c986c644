"""
Intelligent Interest Tracking System.
Learns user interests over time and provides relevant updates.
"""

import random
import re
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    from ..config import config
    from ..core import LoggerMixin, get_current_timestamp
    from ..database import DatabaseManager
except ImportError:
    from config import config
    from core import LoggerMixin, get_current_timestamp
    from database import DatabaseManager


class IntelligentInterestTracker(LoggerMixin):
    """
    Advanced Interest Tracking & World Awareness System.
    - Learns user interests over time
    - Tracks trending topics and current events
    - Proactively shares relevant information like a human friend
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize interest tracker."""
        self.db_manager = db_manager
        
        # Interest categories and their keywords
        self.interest_categories = {
            'technology': ['ai', 'tech', 'software', 'programming', 'computer', 'app', 'startup', 'innovation'],
            'entertainment': ['movie', 'film', 'music', 'song', 'artist', 'celebrity', 'show', 'series', 'netflix'],
            'sports': ['cricket', 'football', 'tennis', 'match', 'tournament', 'player', 'team', 'game'],
            'education': ['study', 'college', 'university', 'course', 'exam', 'degree', 'learning', 'research'],
            'health': ['fitness', 'exercise', 'diet', 'health', 'wellness', 'medical', 'doctor', 'nutrition'],
            'travel': ['travel', 'trip', 'vacation', 'place', 'city', 'country', 'tourism', 'adventure'],
            'food': ['food', 'restaurant', 'cooking', 'recipe', 'cuisine', 'coffee', 'tea', 'dining'],
            'business': ['business', 'work', 'job', 'career', 'company', 'startup', 'economy', 'finance'],
            'science': ['science', 'research', 'discovery', 'space', 'physics', 'biology', 'chemistry'],
            'politics': ['politics', 'government', 'election', 'policy', 'news', 'current affairs'],
            'lifestyle': ['fashion', 'style', 'beauty', 'home', 'design', 'shopping', 'trends'],
            'gaming': ['game', 'gaming', 'esports', 'console', 'pc gaming', 'mobile games']
        }
        
        # Trending topics cache
        self.trending_cache = {}
        self.max_cache_size = 100
        self.last_trend_update = None
        
        # Initialize database schema
        self._initialize_interest_tracking_db()
        
        self.logger.info("Interest tracker initialized")
    
    def _initialize_interest_tracking_db(self) -> None:
        """Initialize database schema for interest tracking."""
        try:
            # User interests table
            query = """
                CREATE TABLE IF NOT EXISTS user_interests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    category TEXT NOT NULL,
                    interest_name TEXT NOT NULL,
                    confidence_score REAL DEFAULT 0.1,
                    mention_count INTEGER DEFAULT 1,
                    first_detected TEXT NOT NULL,
                    last_mentioned TEXT NOT NULL,
                    engagement_level REAL DEFAULT 0.5,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    UNIQUE(user_id, interest_name)
                )
            """
            self.db_manager.execute_update(query)
            
            # Trending topics table
            query = """
                CREATE TABLE IF NOT EXISTS trending_topics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    category TEXT,
                    trend_score REAL DEFAULT 0.0,
                    source TEXT NOT NULL,
                    description TEXT,
                    url TEXT,
                    detected_at TEXT NOT NULL,
                    expires_at TEXT,
                    is_active BOOLEAN DEFAULT 1
                )
            """
            self.db_manager.execute_update(query)
            
            # Interest updates table
            query = """
                CREATE TABLE IF NOT EXISTS interest_updates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    interest_name TEXT NOT NULL,
                    update_type TEXT NOT NULL,
                    content TEXT NOT NULL,
                    source TEXT,
                    relevance_score REAL DEFAULT 0.5,
                    shared_at TEXT,
                    user_reaction TEXT,
                    created_at TEXT NOT NULL
                )
            """
            self.db_manager.execute_update(query)
            
            self.logger.info("Interest tracking database schema initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing interest tracking database: {e}")
    
    def extract_interests_from_text(self, text: str, user_id: str) -> List[Dict[str, Any]]:
        """Extract potential interests from user text."""
        try:
            text_lower = text.lower()
            detected_interests = []
            
            # Check each category for keyword matches
            for category, keywords in self.interest_categories.items():
                for keyword in keywords:
                    if keyword in text_lower:
                        # Calculate confidence based on context
                        confidence = self._calculate_interest_confidence(text, keyword, category)
                        
                        if confidence > 0.3:  # Minimum confidence threshold
                            detected_interests.append({
                                'category': category,
                                'interest_name': keyword,
                                'confidence_score': confidence,
                                'context': text[:200]  # Store context
                            })
            
            # Extract explicit interests (e.g., "I like...", "I love...")
            explicit_patterns = [
                r"i (?:like|love|enjoy|am interested in|am into) ([^.!?]+)",
                r"my (?:hobby|hobbies|interest|interests) (?:is|are|include) ([^.!?]+)",
                r"i'm (?:passionate about|really into|obsessed with) ([^.!?]+)"
            ]
            
            for pattern in explicit_patterns:
                matches = re.findall(pattern, text_lower)
                for match in matches:
                    # Clean and categorize the match
                    interest_text = match.strip()
                    category = self._categorize_interest(interest_text)
                    
                    detected_interests.append({
                        'category': category,
                        'interest_name': interest_text,
                        'confidence_score': 0.8,  # High confidence for explicit mentions
                        'context': text[:200]
                    })
            
            # Store detected interests
            for interest in detected_interests:
                self._store_user_interest(user_id, interest)
            
            return detected_interests
            
        except Exception as e:
            self.logger.error(f"Error extracting interests: {e}")
            return []
    
    def _calculate_interest_confidence(self, text: str, keyword: str, category: str) -> float:
        """Calculate confidence score for detected interest."""
        confidence = 0.1  # Base confidence
        
        # Boost for explicit positive sentiment
        positive_indicators = ['love', 'like', 'enjoy', 'excited', 'passionate', 'interested']
        if any(indicator in text.lower() for indicator in positive_indicators):
            confidence += 0.4
        
        # Boost for personal pronouns
        if any(pronoun in text.lower() for pronoun in ['i', 'my', 'me']):
            confidence += 0.2
        
        # Boost for question about the topic
        if '?' in text and keyword in text.lower():
            confidence += 0.1
        
        # Boost for multiple related keywords
        related_keywords = self.interest_categories.get(category, [])
        related_count = sum(1 for kw in related_keywords if kw in text.lower())
        confidence += min(0.3, related_count * 0.1)
        
        return min(1.0, confidence)
    
    def _categorize_interest(self, interest_text: str) -> str:
        """Categorize an interest based on keywords."""
        text_lower = interest_text.lower()
        
        for category, keywords in self.interest_categories.items():
            if any(keyword in text_lower for keyword in keywords):
                return category
        
        return 'general'
    
    def _store_user_interest(self, user_id: str, interest: Dict[str, Any]) -> None:
        """Store or update user interest in database."""
        try:
            current_time = get_current_timestamp()
            
            # Check if interest already exists
            query = """
                SELECT id, confidence_score, mention_count, engagement_level
                FROM user_interests
                WHERE user_id = ? AND interest_name = ?
            """
            
            existing = self.db_manager.execute_query(
                query, (user_id, interest['interest_name'])
            )
            
            if existing:
                # Update existing interest
                existing_interest = existing[0]
                new_confidence = min(1.0, existing_interest['confidence_score'] + 0.1)
                new_mention_count = existing_interest['mention_count'] + 1
                new_engagement = min(1.0, existing_interest['engagement_level'] + 0.05)
                
                update_query = """
                    UPDATE user_interests
                    SET confidence_score = ?, mention_count = ?, last_mentioned = ?,
                        engagement_level = ?, is_active = 1
                    WHERE id = ?
                """
                
                self.db_manager.execute_update(
                    update_query,
                    (new_confidence, new_mention_count, current_time,
                     new_engagement, existing_interest['id'])
                )
                
            else:
                # Insert new interest
                insert_query = """
                    INSERT INTO user_interests
                    (user_id, category, interest_name, confidence_score, mention_count,
                     first_detected, last_mentioned, engagement_level, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                self.db_manager.execute_update(
                    insert_query,
                    (user_id, interest['category'], interest['interest_name'],
                     interest['confidence_score'], 1, current_time, current_time,
                     0.5, current_time)
                )
            
        except Exception as e:
            self.logger.error(f"Error storing user interest: {e}")
    
    def get_user_interests(self, user_id: str, min_confidence: float = 0.3) -> List[Dict[str, Any]]:
        """Get user's interests above confidence threshold."""
        try:
            query = """
                SELECT category, interest_name, confidence_score, mention_count,
                       engagement_level, last_mentioned
                FROM user_interests
                WHERE user_id = ? AND confidence_score >= ? AND is_active = 1
                ORDER BY confidence_score DESC, mention_count DESC
            """
            
            interests = self.db_manager.execute_query(query, (user_id, min_confidence))
            return interests
            
        except Exception as e:
            self.logger.error(f"Error getting user interests: {e}")
            return []
    
    def should_share_interest_update(self, user_id: str) -> bool:
        """Determine if we should share an interest-based update."""
        try:
            # Check if user has any strong interests
            interests = self.get_user_interests(user_id, min_confidence=0.5)
            if not interests:
                return False
            
            # Random chance based on configuration
            if random.random() > 0.15:  # 15% chance
                return False
            
            # Check if we've shared recently
            query = """
                SELECT COUNT(*) as count
                FROM interest_updates
                WHERE user_id = ? AND shared_at > datetime('now', '-24 hours')
            """
            
            recent_updates = self.db_manager.execute_query(query, (user_id,))
            if recent_updates and recent_updates[0]['count'] > 2:
                return False  # Don't spam with updates
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error determining if should share update: {e}")
            return False
    
    def generate_interest_update(self, user_id: str) -> Optional[str]:
        """Generate a relevant interest-based update for the user."""
        try:
            interests = self.get_user_interests(user_id, min_confidence=0.5)
            if not interests:
                return None
            
            # Select a random interest to share about
            selected_interest = random.choice(interests)
            category = selected_interest['category']
            interest_name = selected_interest['interest_name']
            
            # Generate contextual update based on category
            updates = self._get_category_updates(category, interest_name)
            
            if updates:
                selected_update = random.choice(updates)
                
                # Store the update
                self._store_interest_update(user_id, interest_name, selected_update)
                
                return selected_update
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error generating interest update: {e}")
            return None
    
    def _get_category_updates(self, category: str, interest_name: str) -> List[str]:
        """Get relevant updates for a category."""
        updates = {
            'technology': [
                f"Saw an interesting article about {interest_name} developments lately!",
                f"There's been some cool innovation in {interest_name} recently.",
                f"The {interest_name} space is evolving so fast these days!"
            ],
            'entertainment': [
                f"Have you heard about the latest {interest_name} news?",
                f"There's some buzz around {interest_name} in the entertainment world.",
                f"Saw something cool related to {interest_name} trending today!"
            ],
            'sports': [
                f"Did you catch the latest {interest_name} updates?",
                f"Some exciting {interest_name} action happening lately!",
                f"The {interest_name} season is getting interesting!"
            ],
            'food': [
                f"Discovered a new {interest_name} place that looks amazing!",
                f"Saw an interesting {interest_name} recipe that caught my eye.",
                f"The {interest_name} scene in Bangalore is always evolving!"
            ]
        }
        
        return updates.get(category, [
            f"Came across something interesting about {interest_name}!",
            f"There's been some activity in the {interest_name} space lately.",
            f"Thought you might find this {interest_name} update interesting!"
        ])
    
    def _store_interest_update(self, user_id: str, interest_name: str, content: str) -> None:
        """Store interest update in database."""
        try:
            query = """
                INSERT INTO interest_updates
                (user_id, interest_name, update_type, content, source,
                 relevance_score, shared_at, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            current_time = get_current_timestamp()
            
            self.db_manager.execute_update(
                query,
                (user_id, interest_name, 'proactive_share', content,
                 'ai_friend', 0.7, current_time, current_time)
            )
            
        except Exception as e:
            self.logger.error(f"Error storing interest update: {e}")
    
    def update_interest_engagement(self, user_id: str, interest_name: str, 
                                 reaction: str) -> None:
        """Update interest engagement based on user reaction."""
        try:
            engagement_change = 0.0
            
            if reaction in ['positive', 'interested', 'excited']:
                engagement_change = 0.1
            elif reaction in ['negative', 'uninterested', 'bored']:
                engagement_change = -0.1
            
            if engagement_change != 0.0:
                query = """
                    UPDATE user_interests
                    SET engagement_level = MAX(0.0, MIN(1.0, engagement_level + ?))
                    WHERE user_id = ? AND interest_name = ?
                """
                
                self.db_manager.execute_update(
                    query, (engagement_change, user_id, interest_name)
                )
            
        except Exception as e:
            self.logger.error(f"Error updating interest engagement: {e}")
    
    def get_interest_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of user's interests for context building."""
        try:
            interests = self.get_user_interests(user_id)
            
            # Group by category
            by_category = defaultdict(list)
            for interest in interests:
                by_category[interest['category']].append(interest['interest_name'])
            
            # Get top interests
            top_interests = [i['interest_name'] for i in interests[:5]]
            
            return {
                'top_interests': top_interests,
                'by_category': dict(by_category),
                'total_interests': len(interests),
                'most_engaged_category': max(by_category.keys(), key=lambda k: len(by_category[k])) if by_category else None
            }
            
        except Exception as e:
            self.logger.error(f"Error getting interest summary: {e}")
            return {}
