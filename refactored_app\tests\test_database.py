"""
Tests for database functionality.
Example test file showing how to test the refactored application.
"""

import pytest
import tempfile
import os
from unittest.mock import patch

from refactored_app.database import DatabaseManager
from refactored_app.core import DatabaseError


class TestDatabaseManager:
    """Test cases for DatabaseManager."""
    
    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        yield db_path
        
        # Cleanup
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    def test_database_initialization(self, temp_db):
        """Test database initialization and schema creation."""
        db_manager = DatabaseManager(temp_db)
        
        # Test health check
        assert db_manager.health_check() is True
    
    def test_user_creation_and_retrieval(self, temp_db):
        """Test user creation and retrieval."""
        db_manager = DatabaseManager(temp_db)
        
        # Create user
        user_data = {
            'user_id': 'test_user_123',
            'username': 'testuser',
            'password_hash': 'hashed_password',
            'name': 'Test User'
        }
        
        user_id = db_manager.create_user(user_data)
        assert user_id == 'test_user_123'
        
        # Retrieve user
        retrieved_user = db_manager.get_user_by_id('test_user_123')
        assert retrieved_user is not None
        assert retrieved_user['username'] == 'testuser'
        assert retrieved_user['name'] == 'Test User'
    
    def test_user_update(self, temp_db):
        """Test user profile updates."""
        db_manager = DatabaseManager(temp_db)
        
        # Create user
        user_data = {
            'user_id': 'test_user_456',
            'username': 'testuser2',
            'password_hash': 'hashed_password',
            'name': 'Test User 2'
        }
        
        db_manager.create_user(user_data)
        
        # Update user
        updates = {
            'name': 'Updated Name',
            'interests': ['coding', 'music']
        }
        
        success = db_manager.update_user('test_user_456', updates)
        assert success is True
        
        # Verify update
        updated_user = db_manager.get_user_by_id('test_user_456')
        assert updated_user['name'] == 'Updated Name'
        assert updated_user['interests'] == ['coding', 'music']
    
    def test_message_storage(self, temp_db):
        """Test chat message storage and retrieval."""
        db_manager = DatabaseManager(temp_db)
        
        # Create user first
        user_data = {
            'user_id': 'test_user_789',
            'username': 'testuser3',
            'password_hash': 'hashed_password'
        }
        db_manager.create_user(user_data)
        
        # Save message
        message_data = {
            'user_id': 'test_user_789',
            'role': 'user',
            'content': 'Hello, this is a test message',
            'sentiment': 'positive'
        }
        
        message_id = db_manager.save_message(message_data)
        assert message_id > 0
        
        # Retrieve messages
        messages = db_manager.get_recent_messages('test_user_789', limit=10)
        assert len(messages) == 1
        assert messages[0]['content'] == 'Hello, this is a test message'
        assert messages[0]['role'] == 'user'
    
    def test_feedback_storage(self, temp_db):
        """Test feedback storage."""
        db_manager = DatabaseManager(temp_db)
        
        # Create user first
        user_data = {
            'user_id': 'test_user_feedback',
            'username': 'feedbackuser',
            'password_hash': 'hashed_password'
        }
        db_manager.create_user(user_data)
        
        # Save feedback
        feedback_data = {
            'user_id': 'test_user_feedback',
            'rating': 1,
            'feedback_type': 'general',
            'comment': 'Great response!'
        }
        
        feedback_id = db_manager.save_feedback(feedback_data)
        assert feedback_id > 0
    
    def test_database_error_handling(self):
        """Test database error handling."""
        # Test with invalid database path
        with pytest.raises(DatabaseError):
            db_manager = DatabaseManager("/invalid/path/database.db")
            db_manager.execute_query("SELECT 1")
    
    def test_query_execution(self, temp_db):
        """Test query execution methods."""
        db_manager = DatabaseManager(temp_db)
        
        # Test basic query
        results = db_manager.execute_query("SELECT 1 as test_value")
        assert len(results) == 1
        assert results[0]['test_value'] == 1
        
        # Test update query
        affected_rows = db_manager.execute_update(
            "UPDATE user_profile_v4 SET name = ? WHERE user_id = ?",
            ("New Name", "nonexistent_user")
        )
        assert affected_rows == 0  # No rows affected for nonexistent user


if __name__ == "__main__":
    pytest.main([__file__])
