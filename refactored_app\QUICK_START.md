# AI Friend - Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Prerequisites
- Python 3.8+
- Google Gemini API key
- 4GB+ RAM (for ML models)

### 1. Installation

```bash
# Clone/navigate to refactored_app directory
cd refactored_app

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Create environment file
cp .env.example .env

# Edit .env file with your API key
echo "GEMINI_API_KEY=your_actual_api_key_here" > .env
```

### 3. Run the Application

```bash
# Basic run
python -m refactored_app.main

# With custom options
python -m refactored_app.main --host 0.0.0.0 --port 7861 --debug
```

### 4. Access the Application

Open your browser to: `http://localhost:7861`

---

## 🎯 Key Features Available

### ✅ **Core Features**
- User registration and authentication
- Persistent chat history
- Advanced memory system with FAISS
- Emotion analysis and empathy responses
- Interest tracking and personalization

### ✅ **Advanced Features**
- News fetching and sharing
- Rate limiting and spam protection
- Streaming responses
- Memory consolidation
- Proactive interest updates

### ✅ **Production Features**
- Structured logging
- Health checks
- Error handling
- Docker support
- Environment configuration

---

## 🔧 Configuration Options

### Environment Variables
```bash
# Required
GEMINI_API_KEY=your_api_key

# Optional
SERVER_NAME=0.0.0.0
SERVER_PORT=7861
GRADIO_SHARE=false
LOG_LEVEL=INFO
```

### Command Line Options
```bash
python -m refactored_app.main --help

Options:
  --host TEXT        Host to bind server to
  --port INTEGER     Port to bind server to  
  --share           Create public Gradio link
  --debug           Enable debug mode
  --log-level       Set logging level
```

---

## 🐳 Docker Deployment

### Build and Run
```bash
# Build image
docker build -t ai-friend .

# Run container
docker run -p 7861:7861 -e GEMINI_API_KEY=your_key ai-friend
```

### Docker Compose
```bash
# Set your API key in .env file
echo "GEMINI_API_KEY=your_key" > .env

# Run with compose
docker-compose up -d
```

---

## 🧪 Testing

### Run Tests
```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_database.py

# Run with coverage
pytest --cov=refactored_app

# Skip FAISS tests if not available
SKIP_FAISS_TESTS=true pytest
```

### Test Features
```bash
# Test emotion analysis
python -c "
from refactored_app.features import AdvancedEmotionAnalyzer
analyzer = AdvancedEmotionAnalyzer()
result = analyzer.analyze_emotion('I am so happy!')
print(result)
"

# Test memory system
python -c "
from refactored_app.database import DatabaseManager
from refactored_app.memory import FAISSMemoryManager
db = DatabaseManager(':memory:')
memory = FAISSMemoryManager(db)
print('Memory system ready!')
"
```

---

## 📊 Monitoring

### Health Check
```bash
# Check application health
curl http://localhost:7861/health
```

### Logs
```bash
# View logs
tail -f ai_friend.log

# View error logs
tail -f ai_friend_error.log
```

### Performance
```bash
# Monitor memory usage
python -c "
from refactored_app.features import general_rate_limiter
stats = general_rate_limiter.get_global_stats()
print(stats)
"
```

---

## 🔍 Troubleshooting

### Common Issues

**1. FAISS Installation Error**
```bash
# Install FAISS CPU version
pip install faiss-cpu

# Or skip FAISS tests
export SKIP_FAISS_TESTS=true
```

**2. Model Download Issues**
```bash
# Pre-download models
python -c "
from sentence_transformers import SentenceTransformer
model = SentenceTransformer('all-MiniLM-L6-v2')
print('Models downloaded!')
"
```

**3. Permission Errors**
```bash
# Create required directories
mkdir -p logs data
chmod 755 logs data
```

**4. Port Already in Use**
```bash
# Use different port
python -m refactored_app.main --port 8080
```

### Debug Mode
```bash
# Run with debug logging
python -m refactored_app.main --debug --log-level DEBUG
```

---

## 📚 Usage Examples

### Basic Chat
1. Open `http://localhost:7861`
2. Register a new account
3. Start chatting with Mandy!

### Advanced Features
- **Memory**: Mandy remembers your conversations
- **Interests**: Mention your hobbies and interests
- **Emotions**: Express feelings for empathetic responses
- **News**: Ask about topics you're interested in

### API Integration
```python
# Example: Direct integration
from refactored_app import create_app

app = create_app()
# Use app.chat_manager, app.memory_manager, etc.
```

---

## 🎯 Next Steps

### Development
1. **Add Features**: Use the modular architecture
2. **Customize Persona**: Edit `config/settings.py`
3. **Extend Memory**: Add new memory types
4. **Integrate APIs**: Add new data sources

### Production
1. **Setup Monitoring**: Add metrics collection
2. **Configure Scaling**: Use load balancers
3. **Backup Data**: Setup database backups
4. **Security**: Add HTTPS and authentication

### Customization
1. **Persona**: Modify personality in config
2. **UI**: Customize Gradio interface
3. **Models**: Use different ML models
4. **Features**: Enable/disable components

---

## 🆘 Support

### Documentation
- `README.md` - Full documentation
- `FEATURE_COMPARISON.md` - Feature details
- `REFACTORING_SUMMARY.md` - Architecture overview

### Logs
- `ai_friend.log` - Application logs
- `ai_friend_error.log` - Error logs

### Community
- Check GitHub issues
- Review test files for examples
- Examine configuration options

---

**🎉 You're ready to use your refactored AI Friend application!**

The application now includes all original features plus significant enhancements, all in a clean, maintainable, production-ready architecture.
