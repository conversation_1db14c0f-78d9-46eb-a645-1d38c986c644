"""
Time Awareness Manager for conversation timing and patterns.
Tracks user conversation habits and provides natural time-aware responses.
"""

from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List

try:
    from ..config import config
    from ..core import LoggerMixin, get_current_timestamp, time_since_timestamp
    from ..database import DatabaseManager
except Exception:
    from config import config
    from core import LoggerMixin, get_current_timestamp, time_since_timestamp
    from database import DatabaseManager


class TimeAwarenessManager(LoggerMixin):
    """
    Advanced time-awareness system that tracks conversation patterns, timing, and user habits.
    Provides human-like awareness of when users typically chat and how long they've been away.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize time awareness manager."""
        self.db_manager = db_manager
        
        # Active session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'start_time': None,
            'last_activity': None,
            'message_count': 0,
            'session_id': None
        })
        
        # Time gap thresholds (in seconds)
        self.time_gap_thresholds = {
            'minutes': 60,                    # Less than 1 hour
            'hours': 3600 * 24,              # Less than 1 day  
            'days': 3600 * 24 * 7,           # Less than 1 week
            'weeks': 3600 * 24 * 30,         # Less than 1 month
            'months': 3600 * 24 * 365,       # Less than 1 year
            'years': float('inf')             # More than 1 year
        }
        
        # Conversation pattern thresholds
        self.conversation_patterns = {
            'frequent_chatter': 3600 * 2,      # Chats every 2 hours
            'daily_user': 3600 * 24,           # Chats daily
            'regular_user': 3600 * 24 * 3,     # Chats every few days
            'occasional_user': 3600 * 24 * 7,  # Chats weekly
            'rare_user': 3600 * 24 * 30,       # Chats monthly
            'returning_user': float('inf')      # Long gaps
        }
        
        self.logger.info("Time awareness manager initialized")
    
    def start_session(self, user_id: str) -> str:
        """Start a new conversation session for user."""
        try:
            current_time = get_current_timestamp()
            session_id = f"{user_id}_{int(datetime.now().timestamp())}"
            
            self.active_sessions[user_id] = {
                'start_time': current_time,
                'last_activity': current_time,
                'message_count': 0,
                'session_id': session_id
            }
            
            self.logger.debug(f"Started session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"Error starting session for user {user_id}: {e}")
            return f"{user_id}_fallback"
    
    def update_session_activity(self, user_id: str) -> None:
        """Update last activity time for user session."""
        try:
            if user_id in self.active_sessions:
                self.active_sessions[user_id]['last_activity'] = get_current_timestamp()
                self.active_sessions[user_id]['message_count'] += 1
            else:
                # Start new session if none exists
                self.start_session(user_id)
                
        except Exception as e:
            self.logger.error(f"Error updating session activity for user {user_id}: {e}")
    
    def end_session(self, user_id: str) -> None:
        """End conversation session for user."""
        try:
            if user_id in self.active_sessions:
                session_data = self.active_sessions[user_id]
                
                # Save session to database
                self._save_session_to_db(user_id, session_data)
                
                # Remove from active sessions
                del self.active_sessions[user_id]
                
                self.logger.debug(f"Ended session for user {user_id}")
                
        except Exception as e:
            self.logger.error(f"Error ending session for user {user_id}: {e}")
    
    def _save_session_to_db(self, user_id: str, session_data: Dict[str, Any]) -> None:
        """Save session data to database."""
        try:
            query = """
                INSERT INTO user_sessions 
                (user_id, session_id, start_time, end_time, message_count, duration_seconds)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            
            start_time = session_data['start_time']
            end_time = get_current_timestamp()
            message_count = session_data['message_count']
            
            # Calculate duration
            start_dt = datetime.fromisoformat(start_time)
            end_dt = datetime.fromisoformat(end_time)
            duration_seconds = int((end_dt - start_dt).total_seconds())
            
            self.db_manager.execute_update(
                query,
                (user_id, session_data['session_id'], start_time, end_time, 
                 message_count, duration_seconds)
            )
            
        except Exception as e:
            self.logger.error(f"Error saving session to database: {e}")
    
    def get_time_context(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive time context for conversation."""
        try:
            # Get last session info
            last_session = self._get_last_session_info(user_id)
            
            if not last_session:
                return self._default_time_context()
            
            # Calculate time since last chat
            last_chat_time = last_session.get('end_time') or last_session.get('start_time')
            hours_since_last = 0.0
            
            if last_chat_time:
                hours_since_last = time_since_timestamp(last_chat_time) / 3600
            
            # Determine time gap category
            time_gap_category = self._categorize_time_gap(hours_since_last * 3600)
            
            # Determine conversation pattern
            conversation_pattern = self._determine_conversation_pattern(user_id)
            
            # Check if current time is usual chat time
            is_usual_chat_time = self._is_usual_chat_time(user_id)
            
            # Get session count for last week
            session_count_last_week = self._get_recent_session_count(user_id, days=7)
            
            # Get total sessions
            total_sessions = self._get_total_session_count(user_id)
            
            # Generate detailed time info
            detailed_time_info = self._generate_detailed_time_info(
                hours_since_last, last_chat_time, conversation_pattern
            )
            
            return {
                'hours_since_last_chat': hours_since_last,
                'time_gap_category': time_gap_category,
                'conversation_pattern': conversation_pattern,
                'is_usual_chat_time': is_usual_chat_time,
                'session_count_last_week': session_count_last_week,
                'total_sessions': total_sessions,
                'detailed_time_info': detailed_time_info,
                'last_session': last_session
            }
            
        except Exception as e:
            self.logger.error(f"Error getting time context for user {user_id}: {e}")
            return self._default_time_context()
    
    def _get_last_session_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get information about the user's last session."""
        try:
            query = """
                SELECT session_id, start_time, end_time, message_count, duration_seconds
                FROM user_sessions
                WHERE user_id = ?
                ORDER BY start_time DESC
                LIMIT 1
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result:
                return dict(result[0])
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting last session info: {e}")
            return None
    
    def _categorize_time_gap(self, seconds_since_last: float) -> str:
        """Categorize the time gap since last conversation."""
        for category, threshold in self.time_gap_thresholds.items():
            if seconds_since_last < threshold:
                return category
        return 'years'
    
    def _determine_conversation_pattern(self, user_id: str) -> str:
        """Determine user's conversation pattern based on history."""
        try:
            # Get average time between sessions over last month
            query = """
                SELECT start_time
                FROM user_sessions
                WHERE user_id = ? AND start_time > datetime('now', '-30 days')
                ORDER BY start_time DESC
                LIMIT 10
            """
            
            sessions = self.db_manager.execute_query(query, (user_id,))
            
            if len(sessions) < 2:
                return 'new_user'
            
            # Calculate average gap between sessions
            gaps = []
            for i in range(len(sessions) - 1):
                current_time = datetime.fromisoformat(sessions[i]['start_time'])
                next_time = datetime.fromisoformat(sessions[i + 1]['start_time'])
                gap_seconds = (current_time - next_time).total_seconds()
                gaps.append(gap_seconds)
            
            if not gaps:
                return 'new_user'
            
            avg_gap = sum(gaps) / len(gaps)
            
            # Categorize based on average gap
            for pattern, threshold in self.conversation_patterns.items():
                if avg_gap <= threshold:
                    return pattern
            
            return 'returning_user'
            
        except Exception as e:
            self.logger.error(f"Error determining conversation pattern: {e}")
            return 'regular_user'
    
    def _is_usual_chat_time(self, user_id: str) -> bool:
        """Check if current time matches user's usual chat times."""
        try:
            current_hour = datetime.now().hour
            
            # Get user's chat times from last 30 days
            query = """
                SELECT start_time
                FROM user_sessions
                WHERE user_id = ? AND start_time > datetime('now', '-30 days')
            """
            
            sessions = self.db_manager.execute_query(query, (user_id,))
            
            if not sessions:
                return True  # Default to true for new users
            
            # Extract hours from session times
            chat_hours = []
            for session in sessions:
                session_time = datetime.fromisoformat(session['start_time'])
                chat_hours.append(session_time.hour)
            
            if not chat_hours:
                return True
            
            # Check if current hour is within 2 hours of common chat times
            hour_counts = {}
            for hour in chat_hours:
                hour_counts[hour] = hour_counts.get(hour, 0) + 1
            
            # Find most common hours
            common_hours = [hour for hour, count in hour_counts.items() 
                          if count >= len(chat_hours) * 0.2]  # 20% threshold
            
            # Check if current hour is close to common hours
            for common_hour in common_hours:
                if abs(current_hour - common_hour) <= 2:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking usual chat time: {e}")
            return True
    
    def _get_recent_session_count(self, user_id: str, days: int = 7) -> int:
        """Get number of sessions in recent days."""
        try:
            query = """
                SELECT COUNT(*) as count
                FROM user_sessions
                WHERE user_id = ? AND start_time > datetime('now', '-{} days')
            """.format(days)
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result:
                return result[0]['count']
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Error getting recent session count: {e}")
            return 0
    
    def _get_total_session_count(self, user_id: str) -> int:
        """Get total number of sessions for user."""
        try:
            query = """
                SELECT COUNT(*) as count
                FROM user_sessions
                WHERE user_id = ?
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result:
                return result[0]['count']
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Error getting total session count: {e}")
            return 0
    
    def _generate_detailed_time_info(self, hours_since_last: float, 
                                   last_chat_time: Optional[str],
                                   conversation_pattern: str) -> str:
        """Generate detailed time information for context."""
        try:
            if not last_chat_time:
                return "This appears to be our first conversation!"
            
            # Convert hours to human-readable format
            if hours_since_last < 1:
                time_desc = f"{int(hours_since_last * 60)} minutes ago"
            elif hours_since_last < 24:
                time_desc = f"{int(hours_since_last)} hours ago"
            elif hours_since_last < 168:  # 1 week
                days = int(hours_since_last / 24)
                time_desc = f"{days} day{'s' if days != 1 else ''} ago"
            elif hours_since_last < 720:  # 1 month
                weeks = int(hours_since_last / 168)
                time_desc = f"{weeks} week{'s' if weeks != 1 else ''} ago"
            else:
                months = int(hours_since_last / 720)
                time_desc = f"{months} month{'s' if months != 1 else ''} ago"
            
            # Add pattern context
            pattern_context = {
                'frequent_chatter': "You chat with me quite often",
                'daily_user': "You usually chat with me daily",
                'regular_user': "You chat with me regularly",
                'occasional_user': "You chat with me occasionally",
                'rare_user': "You don't chat with me very often",
                'returning_user': "It's been a while since we last chatted",
                'new_user': "We're just getting to know each other"
            }
            
            pattern_desc = pattern_context.get(conversation_pattern, "")
            
            return f"We last chatted {time_desc}. {pattern_desc}."
            
        except Exception as e:
            self.logger.error(f"Error generating detailed time info: {e}")
            return "We've chatted before."
    
    def _default_time_context(self) -> Dict[str, Any]:
        """Return default time context for new users."""
        return {
            'hours_since_last_chat': 0.0,
            'time_gap_category': 'new_user',
            'conversation_pattern': 'new_user',
            'is_usual_chat_time': True,
            'session_count_last_week': 0,
            'total_sessions': 0,
            'detailed_time_info': "This is our first conversation!",
            'last_session': None
        }
    
    def get_time_aware_greeting(self, user_id: str) -> Optional[str]:
        """Generate time-aware greeting based on conversation patterns."""
        try:
            time_context = self.get_time_context(user_id)
            hours_since_last = time_context['hours_since_last_chat']
            pattern = time_context['conversation_pattern']
            
            # Generate appropriate greeting
            if pattern == 'new_user':
                return "Hi there! Nice to meet you! 😊"
            
            if hours_since_last < 2:
                return "Hey again! 😊"
            elif hours_since_last < 24:
                return "Hi! Good to see you back today!"
            elif hours_since_last < 168:  # 1 week
                days = int(hours_since_last / 24)
                return f"Hey! It's been {days} day{'s' if days != 1 else ''}. How have you been?"
            else:
                return "Hey! It's been a while! How have you been?"
            
        except Exception as e:
            self.logger.error(f"Error generating time-aware greeting: {e}")
            return None
