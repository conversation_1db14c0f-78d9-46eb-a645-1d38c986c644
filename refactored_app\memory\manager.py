"""
Memory management system for AI Friend application.
Handles memory storage, retrieval, and consolidation.
"""

import sqlite3
from collections import defaultdict
from typing import Any, Dict, List, Optional

import numpy as np

try:
    from ..config import config
    from ..core import Lo<PERSON><PERSON><PERSON><PERSON>, MemoryError, get_current_timestamp, time_since_timestamp
    from ..database import DatabaseManager
    from .embeddings import get_embedding_manager
except ImportError:
    from config import config
    from core import Lo<PERSON><PERSON><PERSON><PERSON>, MemoryError, get_current_timestamp, time_since_timestamp
    from database import DatabaseManager
    from memory.embeddings import get_embedding_manager


class MemoryManager(LoggerMixin):
    """Manages user memories with embedding-based retrieval."""
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize memory manager.
        
        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self.logger.info("Memory manager initialized")
    
    def create_memory(self, user_id: str, text: str, emotion: str = "neutral", 
                     importance: float = 0.5, memory_type: str = "short_term",
                     related_chat_log_id: Optional[int] = None) -> Optional[int]:
        """
        Create a new memory entry.
        
        Args:
            user_id: User ID
            text: Memory text content
            emotion: Associated emotion
            importance: Importance score (0.0-1.0)
            memory_type: Type of memory ('short_term' or 'long_term')
            related_chat_log_id: Related chat message ID
            
        Returns:
            Memory ID or None on failure
        """
        try:
            # Validate inputs
            if not user_id or not text:
                self.logger.error("Invalid user_id or text for memory creation")
                return None
            
            # Generate embedding
            embedding = get_embedding_manager().get_embedding(text)
            if embedding is None:
                self.logger.error("Failed to generate embedding for memory")
                return None

            # Convert embedding to bytes
            embedding_bytes = get_embedding_manager().embedding_to_bytes(embedding)

            # Prepare memory data
            memory_data = {
                'user_id': user_id,
                'text': text[:10000],  # Limit text length
                'embedding': embedding_bytes,
                'emotion': emotion[:50],
                'importance': max(0.0, min(1.0, importance)),
                'timestamp': get_current_timestamp(),
                'last_accessed': get_current_timestamp(),
                'access_count': 1,
                'related_chat_log_id': related_chat_log_id,
                'memory_type': memory_type if memory_type in ('short_term', 'long_term') else 'short_term'
            }
            
            # Save to database
            query = f"""
                INSERT INTO {config.database.memory_table}
                (user_id, text, embedding, emotion, importance, timestamp, 
                 last_accessed, access_count, related_chat_log_id, memory_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            memory_id = self.db_manager.execute_insert(
                query, 
                (memory_data['user_id'], memory_data['text'], memory_data['embedding'],
                 memory_data['emotion'], memory_data['importance'], memory_data['timestamp'],
                 memory_data['last_accessed'], memory_data['access_count'],
                 memory_data['related_chat_log_id'], memory_data['memory_type'])
            )
            
            self.logger.info(f"Created memory {memory_id} for user {user_id}")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"Error creating memory: {e}")
            return None
    
    def recall_memories(self, user_id: str, query_text: str, 
                       max_memories: int = 5, memory_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Recall memories relevant to query text.
        
        Args:
            user_id: User ID
            query_text: Query text to find relevant memories
            max_memories: Maximum number of memories to return
            memory_type: Optional filter for memory type
            
        Returns:
            List of relevant memory dictionaries
        """
        try:
            # Generate query embedding
            query_embedding = embedding_manager.get_embedding(query_text)
            if query_embedding is None:
                self.logger.error("Failed to generate query embedding")
                return []
            
            # Get user memories from database
            memories = self._get_user_memories(user_id, memory_type)
            if not memories:
                return []
            
            # Calculate similarities and score memories
            scored_memories = []
            
            for memory in memories:
                try:
                    # Convert embedding bytes back to array
                    memory_embedding = get_embedding_manager().bytes_to_embedding(memory['embedding'])

                    # Calculate similarity
                    similarity = get_embedding_manager().cosine_similarity(query_embedding, memory_embedding)

                    # Apply threshold filter
                    if similarity >= config.memory.cosine_similarity_threshold:
                        # Calculate relevance score
                        relevance_score = self._calculate_relevance_score(memory, similarity, query_text)
                        
                        if relevance_score >= config.memory.relevance_threshold:
                            memory['similarity_score'] = similarity
                            memory['relevance_score'] = relevance_score
                            scored_memories.append((relevance_score, memory))
                
                except Exception as e:
                    self.logger.warning(f"Error processing memory {memory.get('id')}: {e}")
                    continue
            
            # Sort by relevance score and limit results
            scored_memories.sort(key=lambda x: x[0], reverse=True)
            relevant_memories = [memory for _, memory in scored_memories[:max_memories]]
            
            # Update access statistics
            if relevant_memories:
                memory_ids = [m['id'] for m in relevant_memories]
                self._update_memory_access(memory_ids)
            
            self.logger.info(f"Recalled {len(relevant_memories)} memories for user {user_id}")
            return relevant_memories
            
        except Exception as e:
            self.logger.error(f"Error recalling memories: {e}")
            return []
    
    def update_memory_importance(self, memory_id: int, importance_change: float) -> bool:
        """
        Update memory importance score.
        
        Args:
            memory_id: Memory ID to update
            importance_change: Change in importance (can be negative)
            
        Returns:
            True if successful
        """
        try:
            query = f"""
                UPDATE {config.database.memory_table}
                SET importance = MAX(0.0, MIN(1.0, importance + ?))
                WHERE id = ?
            """
            
            affected_rows = self.db_manager.execute_update(query, (importance_change, memory_id))
            
            if affected_rows > 0:
                self.logger.info(f"Updated importance for memory {memory_id} by {importance_change}")
                return True
            else:
                self.logger.warning(f"Memory {memory_id} not found for importance update")
                return False
                
        except Exception as e:
            self.logger.error(f"Error updating memory importance: {e}")
            return False
    
    def consolidate_memories(self, user_id: str) -> None:
        """
        Consolidate memories by promoting important ones and pruning less important ones.
        
        Args:
            user_id: User ID to consolidate memories for
        """
        try:
            self.logger.info(f"Starting memory consolidation for user {user_id}")
            
            # Get short-term memory count
            count_query = f"""
                SELECT COUNT(*) as count FROM {config.database.memory_table}
                WHERE user_id = ? AND memory_type = 'short_term'
            """
            
            result = self.db_manager.execute_query(count_query, (user_id,))
            short_term_count = result[0]['count'] if result else 0
            
            if short_term_count <= config.memory.max_short_term_memories:
                self.logger.info(f"No consolidation needed for user {user_id}")
                return
            
            # Get memories for analysis
            memories_query = f"""
                SELECT id, importance, access_count, timestamp, memory_type
                FROM {config.database.memory_table}
                WHERE user_id = ? AND memory_type = 'short_term'
                ORDER BY timestamp ASC
            """
            
            memories = self.db_manager.execute_query(memories_query, (user_id,))
            
            # Determine which memories to promote or prune
            memories_to_promote = []
            memories_to_prune = []
            
            for memory in memories:
                importance = memory['importance']
                access_count = memory['access_count']
                
                # Promotion criteria
                if (importance >= config.memory.long_term_promotion_threshold or 
                    access_count >= 5):
                    memories_to_promote.append(memory['id'])
                
                # Pruning criteria
                elif importance <= 0.2 and access_count <= 2:
                    memories_to_prune.append(memory['id'])
            
            # Promote memories to long-term
            if memories_to_promote:
                promote_query = f"""
                    UPDATE {config.database.memory_table}
                    SET memory_type = 'long_term'
                    WHERE id IN ({','.join(['?'] * len(memories_to_promote))})
                """
                
                self.db_manager.execute_update(promote_query, tuple(memories_to_promote))
                self.logger.info(f"Promoted {len(memories_to_promote)} memories to long-term")
            
            # Prune low-importance memories
            excess_count = short_term_count - config.memory.max_short_term_memories
            if memories_to_prune and len(memories_to_promote) < excess_count:
                prune_count = min(len(memories_to_prune), excess_count - len(memories_to_promote))
                memories_to_delete = memories_to_prune[:prune_count]
                
                delete_query = f"""
                    DELETE FROM {config.database.memory_table}
                    WHERE id IN ({','.join(['?'] * len(memories_to_delete))})
                """
                
                self.db_manager.execute_update(delete_query, tuple(memories_to_delete))
                self.logger.info(f"Pruned {len(memories_to_delete)} low-importance memories")
            
        except Exception as e:
            self.logger.error(f"Error during memory consolidation: {e}")
    
    def get_memory_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Dictionary with memory statistics
        """
        try:
            stats_query = f"""
                SELECT 
                    memory_type,
                    COUNT(*) as count,
                    AVG(importance) as avg_importance,
                    AVG(access_count) as avg_access_count
                FROM {config.database.memory_table}
                WHERE user_id = ?
                GROUP BY memory_type
            """
            
            results = self.db_manager.execute_query(stats_query, (user_id,))
            
            stats = {
                'short_term': {'count': 0, 'avg_importance': 0.0, 'avg_access_count': 0.0},
                'long_term': {'count': 0, 'avg_importance': 0.0, 'avg_access_count': 0.0}
            }
            
            for result in results:
                memory_type = result['memory_type']
                if memory_type in stats:
                    stats[memory_type] = {
                        'count': result['count'],
                        'avg_importance': round(result['avg_importance'], 2),
                        'avg_access_count': round(result['avg_access_count'], 2)
                    }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting memory stats: {e}")
            return {}
    
    def _get_user_memories(self, user_id: str, memory_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get user memories from database."""
        try:
            if memory_type:
                query = f"""
                    SELECT id, text, embedding, emotion, importance, timestamp,
                           last_accessed, access_count, memory_type
                    FROM {config.database.memory_table}
                    WHERE user_id = ? AND memory_type = ?
                    ORDER BY timestamp DESC
                    LIMIT 250
                """
                params = (user_id, memory_type)
            else:
                query = f"""
                    SELECT id, text, embedding, emotion, importance, timestamp,
                           last_accessed, access_count, memory_type
                    FROM {config.database.memory_table}
                    WHERE user_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 250
                """
                params = (user_id,)
            
            return self.db_manager.execute_query(query, params)
            
        except Exception as e:
            self.logger.error(f"Error getting user memories: {e}")
            return []
    
    def _calculate_relevance_score(self, memory: Dict[str, Any], similarity: float, query_text: str) -> float:
        """Calculate relevance score for a memory."""
        try:
            base_score = memory['importance']
            
            # Recency boost
            seconds_ago = time_since_timestamp(memory.get('timestamp'))
            recency_score = max(0.0, 1.0 - (seconds_ago / (30 * 24 * 3600)))  # 30 days
            
            # Access frequency boost
            access_boost = min(0.3, memory['access_count'] * 0.05)
            
            # Combine scores
            relevance_score = (
                base_score * 0.4 +
                similarity * 0.4 +
                recency_score * 0.1 +
                access_boost * 0.1
            )
            
            return min(1.0, relevance_score)
            
        except Exception as e:
            self.logger.warning(f"Error calculating relevance score: {e}")
            return similarity
    
    def _update_memory_access(self, memory_ids: List[int]) -> None:
        """Update access statistics for memories."""
        try:
            if not memory_ids:
                return
            
            current_time = get_current_timestamp()
            
            # Update access count and last accessed time
            query = f"""
                UPDATE {config.database.memory_table}
                SET last_accessed = ?, access_count = access_count + 1
                WHERE id = ?
            """
            
            for memory_id in memory_ids:
                self.db_manager.execute_update(query, (current_time, memory_id))
            
        except Exception as e:
            self.logger.error(f"Error updating memory access: {e}")
