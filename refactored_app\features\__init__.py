"""Advanced features package for AI Friend application."""

# Avoid heavy imports at package import time; import lazily in callers/tests where possible
from .rate_limiter import RateLimiter, ConversationRateLimiter, general_rate_limiter, conversation_rate_limiter
from .emotion_analyzer import AdvancedEmotionAnalyzer
from .interest_tracker import IntelligentInterestTracker
from .news_fetcher import NewsFetcher

__all__ = [
    'RateLimiter',
    'ConversationRateLimiter',
    'general_rate_limiter',
    'conversation_rate_limiter',
    'AdvancedEmotionAnalyzer',
    'IntelligentInterestTracker',
    'NewsFetcher'
]
