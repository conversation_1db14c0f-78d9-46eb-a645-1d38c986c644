"""
Rate Limiting System for AI Friend application.
Implements token bucket algorithm for request rate limiting.
"""

import time
from collections import defaultdict
from threading import Lock
from typing import Dict, Optional, Tuple

try:
    from ..config import config
    from ..core import Logger<PERSON><PERSON>in, RateLimitError
except ImportError:
    from config import config
    from core import LoggerMixin, RateLimitError


class RateLimiter(LoggerMixin):
    """
    Token bucket rate limiter with per-user tracking.
    Prevents abuse while allowing burst requests.
    """
    
    def __init__(self, requests_per_minute: int = None, burst_size: int = None):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_minute: Maximum requests per minute per user
            burst_size: Maximum burst size for token bucket
        """
        self.requests_per_minute = requests_per_minute or config.security.rate_limit_requests
        self.burst_size = burst_size or config.security.rate_limit_burst
        self.window_size = config.security.rate_limit_window  # seconds
        
        # Token bucket parameters
        self.refill_rate = self.requests_per_minute / 60.0  # tokens per second
        
        # Per-user token buckets: user_id -> (tokens, last_refill_time)
        self.buckets: Dict[str, Tuple[float, float]] = defaultdict(lambda: (self.burst_size, time.time()))
        
        # Request history for monitoring: user_id -> list of timestamps
        self.request_history: Dict[str, list] = defaultdict(list)
        
        # Thread safety
        self._lock = Lock()
        
        self.logger.info(f"Rate limiter initialized: {self.requests_per_minute} req/min, burst: {self.burst_size}")
    
    def check_rate_limit(self, user_id: str, action: str = "request") -> bool:
        """
        Check if user is within rate limits.
        
        Args:
            user_id: User identifier
            action: Action being performed (for logging)
            
        Returns:
            True if request is allowed, False if rate limited
            
        Raises:
            RateLimitError: If rate limit is exceeded
        """
        with self._lock:
            current_time = time.time()
            
            # Refill tokens
            tokens, last_refill = self.buckets[user_id]
            time_passed = current_time - last_refill
            tokens = min(self.burst_size, tokens + (time_passed * self.refill_rate))
            
            # Check if request can be served
            if tokens >= 1.0:
                # Consume token
                tokens -= 1.0
                self.buckets[user_id] = (tokens, current_time)
                
                # Record request
                self._record_request(user_id, current_time, action)
                
                return True
            else:
                # Rate limited
                self.logger.warning(f"Rate limit exceeded for user {user_id} (action: {action})")
                self._record_rate_limit(user_id, current_time, action)
                
                raise RateLimitError(f"Rate limit exceeded. Please wait before making more requests.")
    
    def get_remaining_tokens(self, user_id: str) -> float:
        """Get remaining tokens for a user."""
        with self._lock:
            current_time = time.time()
            tokens, last_refill = self.buckets[user_id]
            time_passed = current_time - last_refill
            tokens = min(self.burst_size, tokens + (time_passed * self.refill_rate))
            return tokens
    
    def get_time_until_next_token(self, user_id: str) -> float:
        """Get time in seconds until next token is available."""
        remaining_tokens = self.get_remaining_tokens(user_id)
        if remaining_tokens >= 1.0:
            return 0.0
        
        # Time to get 1 token
        return (1.0 - remaining_tokens) / self.refill_rate
    
    def _record_request(self, user_id: str, timestamp: float, action: str) -> None:
        """Record a successful request."""
        # Add to history
        self.request_history[user_id].append(timestamp)
        
        # Clean old entries (keep last hour)
        cutoff_time = timestamp - 3600
        self.request_history[user_id] = [
            t for t in self.request_history[user_id] if t > cutoff_time
        ]
        
        self.logger.debug(f"Request recorded for user {user_id}: {action}")
    
    def _record_rate_limit(self, user_id: str, timestamp: float, action: str) -> None:
        """Record a rate-limited request."""
        self.logger.info(f"Rate limit hit - User: {user_id}, Action: {action}, Time: {timestamp}")
    
    def get_user_stats(self, user_id: str) -> Dict[str, any]:
        """Get rate limiting statistics for a user."""
        with self._lock:
            current_time = time.time()
            
            # Get current token count
            remaining_tokens = self.get_remaining_tokens(user_id)
            
            # Get request count in last hour
            cutoff_time = current_time - 3600
            recent_requests = [
                t for t in self.request_history.get(user_id, []) 
                if t > cutoff_time
            ]
            
            # Get request count in last minute
            minute_cutoff = current_time - 60
            recent_minute_requests = [
                t for t in recent_requests if t > minute_cutoff
            ]
            
            return {
                'remaining_tokens': round(remaining_tokens, 2),
                'time_until_next_token': round(self.get_time_until_next_token(user_id), 2),
                'requests_last_hour': len(recent_requests),
                'requests_last_minute': len(recent_minute_requests),
                'rate_limit_per_minute': self.requests_per_minute,
                'burst_size': self.burst_size
            }
    
    def reset_user_limits(self, user_id: str) -> None:
        """Reset rate limits for a specific user (admin function)."""
        with self._lock:
            self.buckets[user_id] = (self.burst_size, time.time())
            self.request_history[user_id] = []
            self.logger.info(f"Rate limits reset for user {user_id}")
    
    def cleanup_old_data(self) -> None:
        """Clean up old rate limiting data."""
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - 3600  # 1 hour
            
            # Clean request history
            users_to_remove = []
            for user_id, history in self.request_history.items():
                # Remove old entries
                recent_history = [t for t in history if t > cutoff_time]
                
                if recent_history:
                    self.request_history[user_id] = recent_history
                else:
                    users_to_remove.append(user_id)
            
            # Remove users with no recent activity
            for user_id in users_to_remove:
                del self.request_history[user_id]
                if user_id in self.buckets:
                    del self.buckets[user_id]
            
            if users_to_remove:
                self.logger.info(f"Cleaned up rate limit data for {len(users_to_remove)} inactive users")
    
    def get_global_stats(self) -> Dict[str, any]:
        """Get global rate limiting statistics."""
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - 3600
            
            total_users = len(self.buckets)
            active_users = len([
                user_id for user_id, history in self.request_history.items()
                if any(t > cutoff_time for t in history)
            ])
            
            total_requests_last_hour = sum(
                len([t for t in history if t > cutoff_time])
                for history in self.request_history.values()
            )
            
            return {
                'total_tracked_users': total_users,
                'active_users_last_hour': active_users,
                'total_requests_last_hour': total_requests_last_hour,
                'average_requests_per_active_user': (
                    round(total_requests_last_hour / max(1, active_users), 2)
                ),
                'rate_limit_config': {
                    'requests_per_minute': self.requests_per_minute,
                    'burst_size': self.burst_size,
                    'window_size': self.window_size
                }
            }


class ConversationRateLimiter(RateLimiter):
    """
    Specialized rate limiter for conversation messages.
    Allows for more nuanced rate limiting based on message content.
    """
    
    def __init__(self):
        """Initialize conversation rate limiter with specific settings."""
        super().__init__(
            requests_per_minute=10,  # Allow more messages per minute
            burst_size=5  # Smaller burst for conversations
        )
        
        # Track message lengths and complexity
        self.message_stats: Dict[str, Dict] = defaultdict(lambda: {
            'total_chars': 0,
            'message_count': 0,
            'last_reset': time.time()
        })
    
    def check_message_rate_limit(self, user_id: str, message: str) -> bool:
        """
        Check rate limit for conversation messages with content analysis.
        
        Args:
            user_id: User identifier
            message: Message content
            
        Returns:
            True if message is allowed
        """
        # Basic rate limit check
        if not self.check_rate_limit(user_id, "message"):
            return False
        
        # Additional checks for message content
        message_length = len(message)
        
        # Track message statistics
        with self._lock:
            stats = self.message_stats[user_id]
            current_time = time.time()
            
            # Reset stats every hour
            if current_time - stats['last_reset'] > 3600:
                stats['total_chars'] = 0
                stats['message_count'] = 0
                stats['last_reset'] = current_time
            
            # Update stats
            stats['total_chars'] += message_length
            stats['message_count'] += 1
            
            # Check for spam patterns
            if self._is_spam_pattern(user_id, message, stats):
                self.logger.warning(f"Spam pattern detected for user {user_id}")
                raise RateLimitError("Message appears to be spam. Please wait before sending more messages.")
        
        return True
    
    def _is_spam_pattern(self, user_id: str, message: str, stats: Dict) -> bool:
        """Detect potential spam patterns."""
        # Check for very long messages
        if len(message) > 2000:
            return True
        
        # Check for too many characters in short time
        if stats['total_chars'] > 10000:  # 10k chars per hour
            return True
        
        # Check for repeated characters (simple spam detection)
        if len(set(message.lower())) < len(message) * 0.3 and len(message) > 50:
            return True
        
        return False
    
    def get_message_stats(self, user_id: str) -> Dict[str, any]:
        """Get message statistics for a user."""
        base_stats = self.get_user_stats(user_id)
        
        with self._lock:
            message_stats = self.message_stats[user_id]
            base_stats.update({
                'total_chars_this_hour': message_stats['total_chars'],
                'messages_this_hour': message_stats['message_count'],
                'avg_message_length': (
                    round(message_stats['total_chars'] / max(1, message_stats['message_count']), 2)
                )
            })
        
        return base_stats


# Global rate limiter instances
general_rate_limiter = RateLimiter()
conversation_rate_limiter = ConversationRateLimiter()
