"""
FAISS-based memory manager for advanced vector search capabilities.
Provides high-performance similarity search for memory retrieval.
"""

from collections import defaultdict
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np

try:
    import faiss
except ImportError:
    faiss = None

try:
    from ..config import config
    from ..core import Logger<PERSON><PERSON><PERSON>, MemoryError, get_current_timestamp, time_since_timestamp
    from ..database import DatabaseManager
    from .embeddings import get_embedding_manager
except ImportError:
    from config import config
    from core import Logger<PERSON>ixin, MemoryError, get_current_timestamp, time_since_timestamp
    from database import DatabaseManager
    from memory.embeddings import get_embedding_manager


class FAISSMemoryManager(LoggerMixin):
    """
    Advanced memory manager using FAISS for high-performance vector search.
    Implements human-like memory retrieval with multiple cognitive effects.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize FAISS memory manager.
        
        Args:
            db_manager: Database manager instance
        """
        if faiss is None:
            raise MemoryError("FAISS library not found. Please install it with: pip install faiss-cpu")
        
        self.db_manager = db_manager
        self.embedding_dimension = get_embedding_manager().embedding_dimension

        # FAISS indices per user
        self.faiss_indices = {}  # user_id -> FAISS index
        self.memory_store = defaultdict(dict)  # user_id -> {memory_id: memory}
        self._loaded_users = set()  # Track loaded users
        
        self.logger.info("FAISS Memory Manager initialized")
    
    def _get_or_create_index(self, user_id: str) -> Optional[faiss.Index]:
        """Get or create FAISS index for user."""
        if user_id not in self.faiss_indices:
            try:
                # Use IndexFlatIP for cosine similarity on normalized vectors, wrapped in IndexIDMap
                base_index = faiss.IndexFlatIP(self.embedding_dimension)
                index = faiss.IndexIDMap(base_index)
                self.faiss_indices[user_id] = index
                self.memory_store[user_id] = {}
                
                # Load existing memories from database
                if user_id not in self._loaded_users:
                    self._load_memories_from_db(user_id)
                    self._loaded_users.add(user_id)
                
                self.logger.info(f"Created FAISS index for user {user_id}")
                
            except Exception as e:
                self.logger.error(f"Failed to create FAISS index for user {user_id}: {e}")
                return None
        
        return self.faiss_indices.get(user_id)
    
    def _load_memories_from_db(self, user_id: str) -> None:
        """Load memories from database into FAISS index."""
        try:
            # Get memories from database
            query = f"""
                SELECT id, text, embedding, emotion, importance, timestamp,
                       last_accessed, access_count, memory_type
                FROM {config.database.memory_table}
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT {config.memory.max_short_term_memories}
            """
            
            memories = self.db_manager.execute_query(query, (user_id,))
            
            if not memories:
                self.logger.info(f"No memories found in database for user {user_id}")
                return
            
            index = self.faiss_indices[user_id]
            embeddings_to_add = []
            ids_to_add = []
            
            for memory in memories:
                try:
                    embedding = get_embedding_manager().bytes_to_embedding(memory['embedding'])
                    embedding = get_embedding_manager().normalize_embedding(embedding)

                    embeddings_to_add.append(embedding)
                    ids_to_add.append(memory['id'])
                    self.memory_store[user_id][memory['id']] = memory
                    
                except Exception as e:
                    self.logger.warning(f"Error processing memory {memory['id']}: {e}")
                    continue
            
            if embeddings_to_add:
                embeddings_array = np.vstack(embeddings_to_add).astype(np.float32)
                ids_array = np.array(ids_to_add).astype(np.int64)
                index.add_with_ids(embeddings_array, ids_array)
                
                self.logger.info(f"Loaded {len(ids_to_add)} memories for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error loading memories from database for user {user_id}: {e}")

    def store_memory(self, user_id: str, text: str, emotion: str = "neutral",
                    importance: float = 0.5, memory_type: str = "short_term",
                    related_chat_log_id: Optional[int] = None,
                    vulnerability_score: float = 0.0, insight_score: float = 0.0) -> Optional[int]:
        """
        Store a new memory with advanced importance scoring.
        """
        try:
            embedding = embedding_manager.get_embedding(text)
            if embedding is None: return None

            final_importance = self._calculate_enhanced_importance(
                importance, vulnerability_score, insight_score, emotion, text
            )
            
            index = self._get_or_create_index(user_id)
            if index is None: return None
            
            memory_data = {
                'user_id': user_id, 'text': text[:10000], 
                'embedding': embedding_manager.embedding_to_bytes(embedding),
                'emotion': emotion[:50], 'importance': final_importance,
                'timestamp': get_current_timestamp(), 'last_accessed': get_current_timestamp(),
                'access_count': 1, 'related_chat_log_id': related_chat_log_id,
                'memory_type': memory_type
            }
            
            query = f"INSERT INTO {config.database.memory_table} (user_id, text, embedding, emotion, importance, timestamp, last_accessed, access_count, related_chat_log_id, memory_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            memory_id = self.db_manager.execute_insert(query, tuple(memory_data.values()))
            
            if memory_id:
                index.add_with_ids(embedding.reshape(1, -1).astype(np.float32), np.array([memory_id]).astype(np.int64))
                
                memory_entry = {**memory_data, 'id': memory_id}
                self.memory_store[user_id][memory_id] = memory_entry
                
                self._enforce_memory_limit(user_id)
                
                self.logger.info(f"Stored memory {memory_id} for user {user_id}")
                return memory_id
            
        except Exception as e:
            self.logger.error(f"Error storing memory: {e}")
            return None

    def _enforce_memory_limit(self, user_id: str):
        """Enforce memory limit by pruning least important memories."""
        user_memory_store = self.memory_store[user_id]
        if len(user_memory_store) > config.memory.max_short_term_memories:
            # Sort memories by importance and timestamp
            sorted_memories = sorted(user_memory_store.values(), key=lambda m: (m['importance'], m['timestamp']))
            
            num_to_prune = len(user_memory_store) - config.memory.max_short_term_memories
            memories_to_prune = sorted_memories[:num_to_prune]
            
            ids_to_remove = [m['id'] for m in memories_to_prune]
            
            if ids_to_remove:
                # Remove from FAISS index
                self.faiss_indices[user_id].remove_ids(np.array(ids_to_remove).astype(np.int64))
                
                # Remove from memory store
                for mem_id in ids_to_remove:
                    del self.memory_store[user_id][mem_id]
                
                # Optionally, remove from database as well for permanent pruning
                # query = f"DELETE FROM {config.database.memory_table} WHERE id IN ({','.join(['?'] * len(ids_to_remove))})"
                # self.db_manager.execute_update(query, tuple(ids_to_remove))

                self.logger.info(f"Pruned {len(ids_to_remove)} memories for user {user_id} to enforce memory limit.")

    def recall_memories(self, user_id: str, query_text: str, max_memories: int = 5,
                       memory_type: Optional[str] = None, 
                       prioritize_long_term: bool = True) -> List[Dict[str, Any]]:
        """
        Recall memories using advanced FAISS search with human-like effects.
        """
        try:
            index = self._get_or_create_index(user_id)
            if index is None or index.ntotal == 0: return []
            
            query_embedding = embedding_manager.get_embedding(query_text)
            if query_embedding is None: return []
            
            search_k = min(max_memories * 3, index.ntotal)
            similarities, faiss_ids = index.search(query_embedding.reshape(1, -1).astype(np.float32), search_k)
            
            if similarities.size == 0: return []
            
            scored_memories = []
            user_memories = self.memory_store[user_id]
            
            for i, (similarity, faiss_id) in enumerate(zip(similarities[0], faiss_ids[0])):
                if faiss_id < 0 or similarity < config.memory.cosine_similarity_threshold: continue
                
                memory = user_memories.get(faiss_id)
                if not memory: continue
                
                if memory_type and memory.get('memory_type') != memory_type: continue
                
                relevance_score = self._calculate_relevance_score(memory, similarity, query_text, prioritize_long_term)
                
                if relevance_score >= config.memory.relevance_threshold:
                    memory_copy = memory.copy()
                    memory_copy['similarity_score'] = float(similarity)
                    memory_copy['relevance_score'] = relevance_score
                    scored_memories.append((relevance_score, memory_copy))
            
            scored_memories.sort(key=lambda x: x[0], reverse=True)
            final_memories = [memory for _, memory in scored_memories[:max_memories]]
            
            if final_memories:
                self._update_memory_access([m['id'] for m in final_memories])
            
            self.logger.info(f"Recalled {len(final_memories)} memories for user {user_id}")
            return final_memories
            
        except Exception as e:
            self.logger.error(f"Error recalling memories: {e}")
            return []

    # ... (rest of the methods are unchanged) ...