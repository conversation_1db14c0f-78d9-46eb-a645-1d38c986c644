version: '3.8'

services:
  ai-friend:
    build: .
    ports:
      - "7861:7861"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - SERVER_NAME=0.0.0.0
      - SERVER_PORT=7861
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:7861')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
