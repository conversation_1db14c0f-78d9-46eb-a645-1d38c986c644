#!/usr/bin/env python3
"""
Full AI Friend Application - Complete Refactored Version
Runs the complete application with all advanced features.
"""

import sys
import os
import argparse

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Download required NLTK data
try:
    import nltk
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
    print("✅ NLTK data downloaded")
except ImportError:
    print("⚠️ NLTK not available, skipping downloads")

def main():
    """Main entry point for the full application."""
    parser = argparse.ArgumentParser(description="AI Friend Application - Full Version")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind server to")
    parser.add_argument("--port", type=int, default=7861, help="Port to bind server to")
    parser.add_argument("--share", action="store_true", help="Create public Gradio link")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", help="Set logging level")
    
    args = parser.parse_args()
    
    # Set environment variables from args
    os.environ["SERVER_NAME"] = args.host
    os.environ["SERVER_PORT"] = str(args.port)
    os.environ["GRADIO_SHARE"] = "true" if args.share else "false"
    os.environ["LOG_LEVEL"] = args.log_level.upper()
    
    try:
        print("🚀 Starting FULL AI Friend Application...")
        print("🧠 Loading advanced features:")
        print("   - FAISS Memory System")
        print("   - Emotion Analysis")
        print("   - Interest Tracking")
        print("   - Personality Engine")
        print("   - Relationship Manager")
        print("   - Time Awareness")
        print("   - Festival Tracker")
        print("   - Response Humanizer")
        print("   - News Fetcher")
        print("   - Rate Limiting")
        print(f"📍 Server: http://{args.host}:{args.port}")
        print(f"🔧 Debug mode: {'ON' if args.debug else 'OFF'}")
        print("=" * 60)
        
        # Import and run the full application
        try:
            from ui.interface import create_full_app
            app = create_full_app()
        except ImportError as e:
            print(f"⚠️ Full app import failed: {e}")
            print("🔄 Falling back to simple app...")
            from simple_app import create_app
            app = create_app()
        
        # Launch the Gradio app
        app.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug,
            show_error=True,
            quiet=False
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Some advanced features may not be available.")
        print("🔄 Trying to run with available components...")
        
        try:
            # Try to run the simple version
            from simple_app import create_app
            app = create_app()
            
            app.launch(
                server_name=args.host,
                server_port=args.port,
                share=args.share,
                debug=args.debug,
                show_error=True
            )
            
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {fallback_error}")
            print("\n📋 Please check your dependencies:")
            print("pip install gradio google-generativeai python-dotenv requests bcrypt")
            sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("\n🔍 Debug information:")
        print(f"Python version: {sys.version}")
        print(f"Current directory: {current_dir}")
        
        if args.debug:
            import traceback
            traceback.print_exc()
        
        sys.exit(1)

if __name__ == "__main__":
    main()
