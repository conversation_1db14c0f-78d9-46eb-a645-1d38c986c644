"""Memory management package for AI Friend application."""

# Lazy and safe exports to avoid heavy imports at package import time
from .manager import MemoryManager
from .faiss_manager import FAISSMemoryManager
# EmbeddingManager imports sentence-transformers; import it lazily in consumers
try:
    from .embeddings import embedding_manager, EmbeddingManager
except Exception:  # leave undefined if heavy deps missing
    embedding_manager = None
    class EmbeddingManager:  # type: ignore
        pass

__all__ = ['MemoryManager', 'FAISSMemoryManager', 'EmbeddingManager', 'embedding_manager']
