"""
Personality Engine for adaptive personality traits.
Adapts <PERSON>'s personality based on user interactions and relationship depth.
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta

try:
    from ..config import config
    from ..core import LoggerMixin, get_current_timestamp
    from ..database import DatabaseManager
except Exception:
    from config import config
    from core import LoggerMixin, get_current_timestamp
    from database import DatabaseManager


class PersonalityEngine(LoggerMixin):
    """
    Adaptive personality engine that adjusts <PERSON>'s traits based on user interactions.
    Maintains baseline personality while adapting to individual users.
    """
    
    def __init__(self, base_persona_config: Dict, db_manager: DatabaseManager):
        """Initialize personality engine with base persona configuration."""
        self.db_manager = db_manager
        self.base_persona_config = base_persona_config
        
        # Extract baseline traits from persona config
        self.baseline_traits = {
            k: float(v) for k, v in base_persona_config.get('adaptability', {}).items()
        }
        
        # In-memory cache of adapted traits per user
        self.user_traits: Dict[str, Dict[str, float]] = {}
        
        # Adaptation parameters
        self.adaptation_rate = config.personality.adaptation_rate
        self.decay_rate = config.personality.decay_rate
        
        self.logger.info(f"PersonalityEngine initialized with baseline traits: {self.baseline_traits}")
    
    def _get_user_traits(self, user_id: str) -> Dict[str, float]:
        """Load or initialize traits for a specific user."""
        if user_id not in self.user_traits:
            # Try to load from database first
            saved_traits = self._load_user_traits_from_db(user_id)
            
            if saved_traits:
                self.user_traits[user_id] = saved_traits
                self.logger.debug(f"Loaded saved traits for user {user_id}: {saved_traits}")
            else:
                # Initialize with baseline traits
                self.user_traits[user_id] = self.baseline_traits.copy()
                self.logger.debug(f"Initialized baseline traits for user {user_id}")
        
        return self.user_traits[user_id]
    
    def _load_user_traits_from_db(self, user_id: str) -> Optional[Dict[str, float]]:
        """Load user's adapted personality traits from database."""
        try:
            query = f"""
                SELECT personality_traits, last_updated 
                FROM {config.database.user_table} 
                WHERE user_id = ?
            """
            
            result = self.db_manager.execute_query(query, (user_id,))
            
            if result and result[0]['personality_traits']:
                import json
                traits_data = json.loads(result[0]['personality_traits'])
                
                # Check if traits are recent enough (within 30 days)
                last_updated = result[0]['last_updated']
                if last_updated:
                    last_update_time = datetime.fromisoformat(last_updated)
                    if datetime.now() - last_update_time > timedelta(days=30):
                        self.logger.debug(f"Traits for user {user_id} are outdated, using baseline")
                        return None
                
                return traits_data
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error loading user traits from database: {e}")
            return None
    
    def _save_user_traits(self, user_id: str) -> None:
        """Save user's adapted personality traits to database."""
        try:
            if user_id not in self.user_traits:
                return
            
            import json
            traits_json = json.dumps(self.user_traits[user_id])
            current_time = get_current_timestamp()
            
            query = f"""
                UPDATE {config.database.user_table}
                SET personality_traits = ?, last_updated = ?
                WHERE user_id = ?
            """
            
            self.db_manager.execute_update(query, (traits_json, current_time, user_id))
            self.logger.debug(f"Saved personality traits for user {user_id}")
            
        except Exception as e:
            self.logger.error(f"Error saving user traits to database: {e}")
    
    def adapt_personality(self, user_id: str, interaction_data: Dict[str, Any]) -> None:
        """
        Adapt personality traits based on user interaction.
        
        Args:
            user_id: User identifier
            interaction_data: Data about the interaction including:
                - user_emotion: User's emotional state
                - user_sentiment: Positive/negative sentiment
                - vulnerability_score: How much user opened up
                - reciprocity_signal: Whether user showed interest in Mandy
                - conversation_length: Length of conversation
                - user_style: User's communication style
        """
        try:
            current_traits = self._get_user_traits(user_id)
            
            # Extract interaction parameters
            user_emotion = interaction_data.get('user_emotion', 'neutral')
            user_sentiment = interaction_data.get('user_sentiment', 'neutral')
            vulnerability_score = interaction_data.get('vulnerability_score', 0.0)
            reciprocity_signal = interaction_data.get('reciprocity_signal', False)
            conversation_length = interaction_data.get('conversation_length', 0)
            
            # Adaptation logic based on interaction
            adaptations = {}
            
            # Warmth adaptation
            if vulnerability_score > 0.6:
                adaptations['warmth'] = min(1.0, current_traits.get('warmth', 0.7) + self.adaptation_rate)
            elif user_sentiment == 'negative':
                adaptations['warmth'] = min(1.0, current_traits.get('warmth', 0.7) + self.adaptation_rate * 0.5)
            
            # Empathy adaptation
            if user_emotion in ['sadness', 'fear', 'anger', 'anxiety']:
                adaptations['empathy'] = min(1.0, current_traits.get('empathy', 0.8) + self.adaptation_rate)
            
            # Humor adaptation
            if user_sentiment == 'positive' and conversation_length > 3:
                adaptations['humor'] = min(1.0, current_traits.get('humor', 0.5) + self.adaptation_rate * 0.5)
            elif user_emotion in ['sadness', 'fear', 'anger']:
                adaptations['humor'] = max(0.1, current_traits.get('humor', 0.5) - self.adaptation_rate * 0.3)
            
            # Curiosity adaptation
            if reciprocity_signal:
                adaptations['curiosity'] = min(1.0, current_traits.get('curiosity', 0.6) + self.adaptation_rate * 0.7)
            
            # Formality adaptation (inverse of user's informality)
            user_style = interaction_data.get('user_style', {})
            if user_style.get('formality_level', 0.5) < 0.3:
                adaptations['formality'] = max(0.1, current_traits.get('formality', 0.3) - self.adaptation_rate * 0.2)
            
            # Apply adaptations
            for trait, new_value in adaptations.items():
                if trait in current_traits:
                    old_value = current_traits[trait]
                    current_traits[trait] = new_value
                    self.logger.debug(f"Adapted {trait} for user {user_id}: {old_value:.3f} -> {new_value:.3f}")
            
            # Apply decay to prevent extreme adaptations
            self._apply_trait_decay(user_id)
            
            # Save updated traits periodically
            if len(adaptations) > 0:
                self._save_user_traits(user_id)
            
        except Exception as e:
            self.logger.error(f"Error adapting personality for user {user_id}: {e}")
    
    def _apply_trait_decay(self, user_id: str) -> None:
        """Apply gradual decay to bring traits back toward baseline."""
        if user_id not in self.user_traits:
            return
        
        current_traits = self.user_traits[user_id]
        
        for trait, current_value in current_traits.items():
            baseline_value = self.baseline_traits.get(trait, current_value)
            
            # Calculate decay toward baseline
            if current_value > baseline_value:
                new_value = max(baseline_value, current_value - self.decay_rate)
            elif current_value < baseline_value:
                new_value = min(baseline_value, current_value + self.decay_rate)
            else:
                new_value = current_value
            
            current_traits[trait] = new_value
    
    def get_current_personality(self, user_id: str) -> Dict[str, float]:
        """Get the current adapted personality traits for the user."""
        # Ensure traits are loaded/initialized for the user first
        return self._get_user_traits(user_id).copy()
    
    def get_style_parameters(self, user_id: str, relationship_stage: str) -> Dict[str, Any]:
        """
        Generate style parameters based on current personality and relationship stage.
        
        Args:
            user_id: User identifier
            relationship_stage: Current relationship stage
            
        Returns:
            Dictionary of style parameters for response generation
        """
        try:
            current_traits = self.get_current_personality(user_id)
            
            # Base parameters from traits
            params = {
                'warmth_level': current_traits.get('warmth', 0.7),
                'humor_level': int(current_traits.get('humor', 0.5) * 10),  # Scale to 0-10
                'empathy_level': current_traits.get('empathy', 0.8),
                'curiosity_level': current_traits.get('curiosity', 0.6),
                'formality_level': current_traits.get('formality', 0.3),
                'use_emojis': current_traits.get('warmth', 0.7) > 0.6,
                'use_slang': current_traits.get('formality', 0.3) < 0.4,
            }
            
            # Adjust based on relationship stage
            stage_adjustments = {
                'acquaintance': {
                    'warmth_level': lambda x: min(0.6, x),
                    'humor_level': lambda x: min(4, x),
                    'use_slang': lambda x: False,
                },
                'casual friend': {
                    'warmth_level': lambda x: min(0.8, x + 0.1),
                    'humor_level': lambda x: min(6, x + 1),
                },
                'friend': {
                    'warmth_level': lambda x: min(0.9, x + 0.2),
                    'humor_level': lambda x: min(8, x + 2),
                    'use_slang': lambda x: True,
                },
                'close friend': {
                    'warmth_level': lambda x: min(1.0, x + 0.3),
                    'humor_level': lambda x: min(10, x + 3),
                    'use_slang': lambda x: True,
                }
            }
            
            # Apply stage adjustments
            if relationship_stage in stage_adjustments:
                adjustments = stage_adjustments[relationship_stage]
                for param, adjustment_func in adjustments.items():
                    if param in params:
                        params[param] = adjustment_func(params[param])
            
            self.logger.debug(f"Generated style parameters for user {user_id}: {params}")
            return params
            
        except Exception as e:
            self.logger.error(f"Error generating style parameters: {e}")
            return {
                'warmth_level': 0.7,
                'humor_level': 5,
                'empathy_level': 0.8,
                'curiosity_level': 0.6,
                'formality_level': 0.3,
                'use_emojis': True,
                'use_slang': False,
            }
    
    def reset_user_personality(self, user_id: str) -> None:
        """Reset user's personality traits to baseline."""
        try:
            self.user_traits[user_id] = self.baseline_traits.copy()
            self._save_user_traits(user_id)
            self.logger.info(f"Reset personality traits for user {user_id} to baseline")
            
        except Exception as e:
            self.logger.error(f"Error resetting personality for user {user_id}: {e}")
    
    def get_personality_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of user's personality adaptations."""
        try:
            current_traits = self.get_current_personality(user_id)
            baseline_traits = self.baseline_traits
            
            # Calculate deviations from baseline
            deviations = {}
            for trait, current_value in current_traits.items():
                baseline_value = baseline_traits.get(trait, current_value)
                deviation = current_value - baseline_value
                deviations[trait] = {
                    'current': current_value,
                    'baseline': baseline_value,
                    'deviation': deviation,
                    'adapted': abs(deviation) > 0.05  # Significant adaptation threshold
                }
            
            return {
                'user_id': user_id,
                'current_traits': current_traits,
                'baseline_traits': baseline_traits,
                'deviations': deviations,
                'adaptation_count': sum(1 for d in deviations.values() if d['adapted'])
            }
            
        except Exception as e:
            self.logger.error(f"Error getting personality summary: {e}")
            return {}
