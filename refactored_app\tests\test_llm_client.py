import unittest
from unittest.mock import MagicMock, patch, PropertyMock
from llm.client import <PERSON><PERSON>lient, LLMError

class TestLLMClient(unittest.TestCase):

    def setUp(self):
        with patch('llm.client.config') as mock_config:
            mock_config.llm.api_key = 'test-api-key'
            self.llm_client = LLMClient()

    @patch('google.generativeai.GenerativeModel')
    def test_generate_response_success(self, mock_generative_model):
        mock_response = MagicMock()
        type(mock_response).text = PropertyMock(return_value='Test response')
        mock_generative_model.return_value.generate_content.return_value = mock_response

        response = self.llm_client.generate_response("test prompt")

        self.assertEqual(response, "Test response")
        mock_generative_model.return_value.generate_content.assert_called_once_with("test prompt")

    @patch('google.generativeai.GenerativeModel')
    def test_generate_response_stream_success(self, mock_generative_model):
        mock_chunk1 = MagicMock()
        type(mock_chunk1).text = PropertyMock(return_value='Test')
        mock_chunk2 = MagicMock()
        type(mock_chunk2).text = PropertyMock(return_value=' response')
        mock_generative_model.return_value.generate_content.return_value = [mock_chunk1, mock_chunk2]

        response_generator = self.llm_client.generate_response_stream("test prompt")
        response = "".join(list(response_generator))

        self.assertEqual(response, "Test response")

    @patch('google.generativeai.GenerativeModel')
    def test_generate_json_response_success(self, mock_generative_model):
        mock_response = MagicMock()
        type(mock_response).text = PropertyMock(return_value='{"key": "value"}')
        mock_generative_model.return_value.generate_content.return_value = mock_response

        response = self.llm_client.generate_json_response("test prompt")

        self.assertEqual(response, {"key": "value"})

    @patch('google.generativeai.GenerativeModel')
    def test_retry_on_failure(self, mock_generative_model):
        mock_generative_model.return_value.generate_content.side_effect = [
            Exception("API Error"),
            MagicMock(text="Successful response")
        ]

        response = self.llm_client.generate_response("test prompt")

        self.assertEqual(response, "Successful response")
        self.assertEqual(mock_generative_model.return_value.generate_content.call_count, 2)

    @patch('google.generativeai.GenerativeModel')
    def test_retry_exhausted(self, mock_generative_model):
        mock_generative_model.return_value.generate_content.side_effect = Exception("API Error")

        with self.assertRaises(LLMError):
            self.llm_client.generate_response("test prompt")
        
        self.assertEqual(mock_generative_model.return_value.generate_content.call_count, 3)

if __name__ == '__main__':
    unittest.main()