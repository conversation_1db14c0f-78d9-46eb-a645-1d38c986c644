2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:405 | setup_logging | Structured logging configured for production environment
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:419 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:7569 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:729 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:773 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:844 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:994 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:1585 | __init__ | AuthenticationManager initialized.
2025-08-20 02:32:09 | INFO     | 13544 | 24920 | app.py:4527 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 02:32:15 | WARNING  | 13544 | 24920 | file_download.py:1732 | _download_to_tmp_and_move | Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-20 02:32:34 | INFO     | 13544 | 24920 | app.py:4544 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 02:32:36 | WARNING  | 13544 | 3620 | file_download.py:1732 | _download_to_tmp_and_move | Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-20 02:32:39 | WARNING  | 13544 | 24920 | file_download.py:1732 | _download_to_tmp_and_move | Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-20 02:34:28 | INFO     | 13544 | 24920 | app.py:4554 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 02:34:28 | INFO     | 13544 | 24920 | app.py:458 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 02:34:28 | INFO     | 13544 | 24920 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 02:34:28 | INFO     | 13544 | 24920 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 02:34:30 | WARNING  | 13544 | 21960 | file_download.py:1732 | _download_to_tmp_and_move | Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-20 02:34:31 | WARNING  | 13544 | 24920 | file_download.py:1732 | _download_to_tmp_and_move | Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:482 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:2902 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:1745 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:1678 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:4712 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:4950 | __init__ | RelationshipManager initialized.
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5026 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5076 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5118 | __init__ | NewsFetcher initialized.
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5303 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5378 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:5391 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:7582 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:7583 | <module> | Operating system: win32
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:7587 | <module> | CUDA not available, using CPU
2025-08-20 02:35:09 | INFO     | 13544 | 24920 | app.py:7193 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 02:35:10 | INFO     | 13544 | 24920 | app.py:7437 | create_gradio_interface | Gradio interface created.
2025-08-20 02:35:10 | INFO     | 13544 | 24920 | app.py:7610 | <module> | Launching Gradio interface...
2025-08-20 02:37:42 | INFO     | 13544 | 21204 | app.py:1589 | register_user | Attempting registration for username: dha
2025-08-20 02:37:42 | INFO     | 13544 | 21204 | app.py:1606 | register_user | User 'dha' registered successfully with user_id: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:46 | INFO     | 13544 | 21204 | app.py:1617 | verify_user | Attempting login for username: dha
2025-08-20 02:37:46 | INFO     | 13544 | 21204 | app.py:1633 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 02:37:46 | INFO     | 13544 | 21204 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:46 | INFO     | 13544 | 21204 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:46 | INFO     | 13544 | 21204 | app.py:7188 | get_initial_greeting | Generated greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hey! I'm Mandy. What brings you here today?' (Time since last: 0.0 hrs)
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'HI...'
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'HI...'
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (0/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 1) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:4722 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:2943 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:2971 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:394 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:399 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.00030922889709472656, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 0, "errors": 0}
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:2984 | _load_and_index_user_memories | No memories found in DB for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'.
2025-08-20 02:37:58 | INFO     | 13544 | 23624 | app.py:3278 | recall_related_memories | No index or memories available for recall for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'.
2025-08-20 02:37:59 | INFO     | 13544 | 23624 | app.py:1787 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:37:59 | INFO     | 13544 | 23624 | app.py:1814 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 02:37:59 | INFO     | 13544 | 23624 | app.py:1827 | _load_facts_from_db | No facts found in DB for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'.
2025-08-20 02:37:59 | WARNING  | 13544 | 23624 | app.py:2130 | search_facts | No facts indexed for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:37:59 | INFO     | 13544 | 23624 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:38:05 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Hi there! How are you doing? It feels like ages since we last properly chatted. What's been keeping ...'
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 2) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:38:05 | WARNING  | 13544 | 23624 | app.py:7025 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 1, "memory_type": "short_term"}
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 1, Store Idx: 0, FAISS Idx: 0, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:05 | WARNING  | 13544 | 23624 | app.py:4959 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.00 -> 0.01 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:38:05 | INFO     | 13544 | 23624 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 7.48s)
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'SO MY NAME IS DHANANJAY ...'
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'SO MY NAME IS DHANANJAY ...'
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:5547 | _extract_and_update_profile_info | Extracted potential name 'Dhananjay' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f via regex pattern: (?:my\s+name\s+is|call\s+me|i'm|i\s+am)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: name, last_active
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:5557 | _extract_and_update_profile_info | Saved extracted name 'Dhananjay' to DB for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f.
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (1/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 3) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:38:39 | INFO     | 13544 | 23624 | app.py:3588 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 02:38:39 | WARNING  | 13544 | 23624 | app.py:2130 | search_facts | No facts indexed for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:40 | INFO     | 13544 | 23624 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:38:48 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:38:48 | INFO     | 13544 | 23624 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! That's a lovely name. It's so nice to finally... What brings you to Mindely today? I'...'
2025-08-20 02:38:48 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 4) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:6599 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.17, "memory_type": "short_term", "text_length": 24, "emotion": "realization"}
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 2, "memory_type": "short_term"}
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 2, Store Idx: 1, FAISS Idx: 1, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:38:50 | INFO     | 13544 | 23624 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.01 -> 0.07 (Inc: 0.056 [Vuln:0.022, Len:0.024, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:38:51 | INFO     | 13544 | 23624 | app.py:2099 | add_fact | Added fact for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: The user's name is Dhananjay.... with ID bb872ffd-5a33-4eb8-9059-93ed90b46da5
2025-08-20 02:38:51 | INFO     | 13544 | 23624 | app.py:2401 | extract_facts_from_message | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:51 | INFO     | 13544 | 23624 | app.py:7021 | generate_personalized_response | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:38:51 | INFO     | 13544 | 23624 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 12.09s)
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'I STUDY IN BIT MESRA IN PROD BRANCH ...'
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'I STUDY IN BIT MESRA IN PROD BRANCH ...'
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (2/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 5) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:39:56 | INFO     | 13544 | 23624 | app.py:3588 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 02:39:57 | INFO     | 13544 | 23624 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:40:08 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:40:08 | INFO     | 13544 | 23624 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Oh wow, BIT Mesra! That's a really impressive place to be studying, Dhananjay. Production engineerin...'
2025-08-20 02:40:08 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 6) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:6599 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.16, "memory_type": "short_term", "text_length": 36, "emotion": "caring"}
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 3, "memory_type": "short_term"}
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 3, Store Idx: 2, FAISS Idx: 2, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:40:10 | INFO     | 13544 | 23624 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.07 -> 0.14 (Inc: 0.070 [Vuln:0.022, Len:0.037, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:40:11 | INFO     | 13544 | 23624 | app.py:2099 | add_fact | Added fact for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: The user studies at BIT Mesra in the Production En... with ID 4a34ad4a-b87f-4417-aa96-115c87864930
2025-08-20 02:40:11 | INFO     | 13544 | 23624 | app.py:2401 | extract_facts_from_message | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:11 | INFO     | 13544 | 23624 | app.py:7021 | generate_personalized_response | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:11 | INFO     | 13544 | 23624 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 14.73s)
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'WHERE DO I STUDY...'
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:4050 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 100)
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:4068 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 3
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'WHERE DO I STUDY...'
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (3/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 7) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:40:36 | INFO     | 13544 | 23624 | app.py:3588 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 02:40:37 | INFO     | 13544 | 23624 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:40:47 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:40:47 | INFO     | 13544 | 23624 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! You're at BIT Mesra, right? In Production Engineering. That's... quite the undertakin...'
2025-08-20 02:40:47 | INFO     | 13544 | 23624 | app.py:1159 | log_chat_message | Chat message logged (ID: 8) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:6599 | _summarize_interaction | Interaction summarized. Vuln=0.00, Reci=False, Insights=1
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.17, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 4, "memory_type": "short_term"}
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 4, Store Idx: 3, FAISS Idx: 3, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:40:48 | INFO     | 13544 | 23624 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.14 -> 0.20 (Inc: 0.068 [Vuln:0.000, Len:0.058, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:40:49 | INFO     | 13544 | 27376 | app.py:7417 | handle_logout | User logging out.
2025-08-20 02:40:49 | INFO     | 13544 | 23624 | app.py:2401 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:40:49 | INFO     | 13544 | 23624 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 12.98s)
2025-08-20 02:41:03 | INFO     | 13544 | 23084 | app.py:1617 | verify_user | Attempting login for username: DHA
2025-08-20 02:41:03 | WARNING  | 13544 | 23084 | app.py:1639 | verify_user | Login failed: Username 'DHA' not found.
2025-08-20 02:41:10 | INFO     | 13544 | 23084 | app.py:1617 | verify_user | Attempting login for username: dha
2025-08-20 02:41:10 | INFO     | 13544 | 23084 | app.py:1633 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 02:41:10 | INFO     | 13544 | 23084 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:10 | INFO     | 13544 | 23084 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:10 | INFO     | 13544 | 23084 | app.py:7188 | get_initial_greeting | Generated greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hi again, Dhananjay! What's up now?' (Time since last: 0.0 hrs)
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'where do i study...'
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'where do i study...'
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (4/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1159 | log_chat_message | Chat message logged (ID: 9) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:1541 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-08-20 02:41:34 | INFO     | 13544 | 21168 | app.py:3588 | recall_related_memories | Recalled 1 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 1 candidates)
2025-08-20 02:41:35 | INFO     | 13544 | 21168 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:41:47 | ERROR    | 13544 | 21168 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:41:47 | INFO     | 13544 | 21168 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Oh, hey Dhananjay! Where *do* you want to study? That sounds like a big question – it’s kind of exci...'
2025-08-20 02:41:48 | INFO     | 13544 | 21168 | app.py:1159 | log_chat_message | Chat message logged (ID: 10) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:41:49 | INFO     | 13544 | 21168 | app.py:6599 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 02:41:49 | INFO     | 13544 | 21168 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.21, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 02:41:49 | INFO     | 13544 | 21168 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 5, "memory_type": "short_term"}
2025-08-20 02:41:50 | INFO     | 13544 | 21168 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 5, Store Idx: 4, FAISS Idx: 4, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:41:50 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:50 | INFO     | 13544 | 21168 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:41:50 | INFO     | 13544 | 21168 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.20 -> 0.31 (Inc: 0.103 [Vuln:0.045, Len:0.048, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:41:51 | INFO     | 13544 | 21168 | app.py:2401 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:41:51 | INFO     | 13544 | 21168 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 17.14s)
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:7339 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'which collage do i study...'
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:6624 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'which collage do i study...'
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:5423 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:5456 | _analyze_and_update_user_style | Insufficient user messages (5/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1159 | log_chat_message | Chat message logged (ID: 11) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1246 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:1541 | record_memory_access | Recorded access attempt for 2 memory entries (requested: 2).
2025-08-20 02:42:33 | INFO     | 13544 | 21168 | app.py:3588 | recall_related_memories | Recalled 2 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 2 candidates)
2025-08-20 02:42:34 | INFO     | 13544 | 21168 | app.py:5853 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 02:42:45 | ERROR    | 13544 | 21168 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:42:45 | INFO     | 13544 | 21168 | app.py:6775 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! Choosing a college is HUGE. It feels like that, right? I remember feeling completely ...'
2025-08-20 02:42:45 | INFO     | 13544 | 21168 | app.py:1159 | log_chat_message | Chat message logged (ID: 12) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:6599 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=2
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:394 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.27, "memory_type": "short_term", "text_length": 24, "emotion": "curiosity"}
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:399 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 6, "memory_type": "short_term"}
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:3166 | store_interaction | Stored memory successfully (DB ID: 6, Store Idx: 5, FAISS Idx: 5, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:1105 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:1063 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 02:42:47 | INFO     | 13544 | 21168 | app.py:5001 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.31 -> 0.41 (Inc: 0.105 [Vuln:0.045, Len:0.050, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 02:42:48 | INFO     | 13544 | 21168 | app.py:2401 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 02:42:48 | INFO     | 13544 | 21168 | app.py:7037 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 14.56s)
2025-08-20 03:00:23 | INFO     | 13544 | 24920 | app.py:7635 | <module> | Gradio application closed.
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:442 | setup_logging | Structured logging configured for production environment
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:456 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:7809 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:766 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:810 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:881 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:1035 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:1626 | __init__ | AuthenticationManager initialized.
2025-08-20 18:35:21 | INFO     | 27996 | 31040 | app.py:4765 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 18:35:24 | INFO     | 27996 | 31040 | app.py:4782 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 18:35:28 | INFO     | 27996 | 31040 | app.py:4792 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 18:35:28 | INFO     | 27996 | 31040 | app.py:495 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 18:35:28 | INFO     | 27996 | 31040 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 18:35:28 | INFO     | 27996 | 31040 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:519 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:2943 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:1786 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:1719 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:4950 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5188 | __init__ | RelationshipManager initialized.
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5264 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5314 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5356 | __init__ | NewsFetcher initialized.
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5541 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5616 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:5629 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7822 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7823 | <module> | Operating system: win32
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7827 | <module> | CUDA not available, using CPU
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7433 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7677 | create_gradio_interface | Gradio interface created.
2025-08-20 18:35:34 | INFO     | 27996 | 31040 | app.py:7850 | <module> | Launching Gradio interface...
2025-08-20 18:36:15 | INFO     | 27996 | 1652 | app.py:1658 | verify_user | Attempting login for username: dha
2025-08-20 18:36:16 | INFO     | 27996 | 1652 | app.py:1674 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 18:36:16 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:16 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:16 | INFO     | 27996 | 1652 | app.py:7428 | get_initial_greeting | Generated greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hey again, Dhananjay! What's up now?' (Time since last: 0.0 hrs)
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:7579 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:6862 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:5661 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:5694 | _analyze_and_update_user_style | Insufficient user messages (6/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1200 | log_chat_message | Chat message logged (ID: 13) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:4960 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1287 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:2984 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:3012 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:431 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:436 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0003743171691894531, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 6, "errors": 0}
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:3091 | _load_and_index_user_memories | Successfully loaded and indexed 6/6 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1582 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:3725 | recall_related_memories | Recalled 1 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 1 candidates)
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1828 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1855 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:36:19 | INFO     | 27996 | 1652 | app.py:1919 | _load_facts_from_db | Loaded 2 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:36:20 | INFO     | 27996 | 1652 | app.py:6091 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:36:27 | ERROR    | 27996 | 1652 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:7013 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! 😊 How are you doing? It feels like ages since we last chatted – or maybe it was just y...'
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:1200 | log_chat_message | Chat message logged (ID: 14) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:36:27 | WARNING  | 27996 | 1652 | app.py:7263 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:431 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:436 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 7, "memory_type": "short_term"}
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:3248 | _store_single_memory | Stored memory successfully (DB ID: 7, Store Idx: 6, FAISS Idx: 6, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:3148 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:36:27 | WARNING  | 27996 | 1652 | app.py:5197 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:1104 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:5239 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.41 -> 0.42 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:36:27 | INFO     | 27996 | 1652 | app.py:7275 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 8.62s)
2025-08-20 18:37:07 | INFO     | 27996 | 1652 | app.py:7579 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'i study in production enginriing in bt mesra...'
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:6862 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'i study in production enginriing in bt mesra...'
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:5661 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:5694 | _analyze_and_update_user_style | Insufficient user messages (7/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 15) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1287 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:1582 | record_memory_access | Recorded access attempt for 4 memory entries (requested: 4).
2025-08-20 18:37:07 | INFO     | 27996 | 23944 | app.py:3725 | recall_related_memories | Recalled 4 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 1 candidates)
2025-08-20 18:37:08 | INFO     | 27996 | 23944 | app.py:6091 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:37:20 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:37:20 | INFO     | 27996 | 23944 | app.py:7013 | generate_personalized_response | Final Humanized Response: 'Oh, wow, Production Engineering at BIT Mesra! That sounds intense. It reminds me of how you were fee...'
2025-08-20 18:37:20 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 16) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:6837 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:431 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.18, "memory_type": "short_term", "text_length": 44, "emotion": "approval"}
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:436 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 8, "memory_type": "short_term"}
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:3248 | _store_single_memory | Stored memory successfully (DB ID: 8, Store Idx: 7, FAISS Idx: 7, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:431 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.53, "memory_type": "micro_detail", "text_length": 53, "emotion": "approval"}
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:436 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 9, "memory_type": "micro_detail"}
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:3248 | _store_single_memory | Stored memory successfully (DB ID: 9, Store Idx: 8, FAISS Idx: 8, Type: micro_detail) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:3148 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:1104 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:37:21 | INFO     | 27996 | 23944 | app.py:5239 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.42 -> 0.51 (Inc: 0.090 [Vuln:0.045, Len:0.035, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:37:22 | INFO     | 27996 | 23944 | app.py:2140 | add_fact | Added fact for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: The user studies production engineering.... with ID c49ee381-b719-4a15-a2b2-0594f59fbdb9
2025-08-20 18:37:22 | INFO     | 27996 | 23944 | app.py:2140 | add_fact | Added fact for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: The user studies at BIT Mesra.... with ID a7303882-e394-41df-8049-201c1e367117
2025-08-20 18:37:22 | INFO     | 27996 | 23944 | app.py:2442 | extract_facts_from_message | Extracted 2 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:22 | INFO     | 27996 | 23944 | app.py:7259 | generate_personalized_response | Extracted 2 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:37:22 | INFO     | 27996 | 23944 | app.py:7275 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 15.32s)
2025-08-20 18:38:55 | INFO     | 27996 | 23944 | app.py:7657 | handle_logout | User logging out.
2025-08-20 18:39:01 | INFO     | 27996 | 23944 | app.py:1658 | verify_user | Attempting login for username: dha
2025-08-20 18:39:01 | INFO     | 27996 | 23944 | app.py:1674 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 18:39:01 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:01 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:01 | INFO     | 27996 | 23944 | app.py:7428 | get_initial_greeting | Generated greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya again, Dhananjay! What's up now?' (Time since last: 0.0 hrs)
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:7579 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:6862 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:5661 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:5694 | _analyze_and_update_user_style | Insufficient user messages (8/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:39:03 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 17) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:1287 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:1582 | record_memory_access | Recorded access attempt for 2 memory entries (requested: 2).
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:3725 | recall_related_memories | Recalled 2 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 2 candidates)
2025-08-20 18:39:04 | INFO     | 27996 | 23944 | app.py:6091 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:39:11 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:7013 | generate_personalized_response | Final Humanized Response: 'Hi, Dhananjay! How are you doing? It feels like ages since we last chatted – or maybe it was just ye...'
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 18) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:39:11 | WARNING  | 27996 | 23944 | app.py:7263 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:431 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:436 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 10, "memory_type": "short_term"}
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:3248 | _store_single_memory | Stored memory successfully (DB ID: 10, Store Idx: 9, FAISS Idx: 9, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:3148 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:11 | WARNING  | 27996 | 23944 | app.py:5197 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:1104 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:5239 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.51 -> 0.52 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:39:11 | INFO     | 27996 | 23944 | app.py:7275 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 7.19s)
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:7579 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'where do i study...'
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:6862 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'where do i study...'
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:5661 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:5694 | _analyze_and_update_user_style | Insufficient user messages (9/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 19) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1287 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:1582 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 18:39:21 | INFO     | 27996 | 23944 | app.py:3725 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 4 candidates)
2025-08-20 18:39:22 | INFO     | 27996 | 23944 | app.py:6091 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:39:38 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:39:38 | INFO     | 27996 | 23944 | app.py:7013 | generate_personalized_response | Final Humanized Response: 'Hey Dhananjay! "Where do I study?" Hmm That's a big question! It sounds like you might be feeling a ...'
2025-08-20 18:39:38 | INFO     | 27996 | 23944 | app.py:1200 | log_chat_message | Chat message logged (ID: 20) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:6837 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:431 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.21, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:436 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 11, "memory_type": "short_term"}
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:3248 | _store_single_memory | Stored memory successfully (DB ID: 11, Store Idx: 10, FAISS Idx: 10, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:3148 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:1146 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:1104 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:5239 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.52 -> 0.62 (Inc: 0.097 [Vuln:0.045, Len:0.042, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:2442 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:39:40 | INFO     | 27996 | 23944 | app.py:7275 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 19.31s)
2025-08-20 18:52:39 | INFO     | 27996 | 31040 | app.py:7875 | <module> | Gradio application closed.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:473 | setup_logging | Structured logging configured for production environment
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:487 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:8291 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:797 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:841 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:912 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'conversation_pattern' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'avg_session_gap_hours' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'preferred_chat_times' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'last_session_duration_minutes' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'total_sessions' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'longest_gap_days' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'time_zone' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1107 | _add_column_if_not_exists | Added column 'session_start_time' to table 'user_profile_v4'.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1095 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:1686 | __init__ | AuthenticationManager initialized.
2025-08-20 18:53:42 | INFO     | 4960 | 33528 | app.py:5129 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 18:53:45 | INFO     | 4960 | 33528 | app.py:5146 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 18:53:49 | INFO     | 4960 | 33528 | app.py:5156 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 18:53:49 | INFO     | 4960 | 33528 | app.py:526 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 18:53:49 | INFO     | 4960 | 33528 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 18:53:49 | INFO     | 4960 | 33528 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:550 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:3307 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:1846 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:1779 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5314 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5552 | __init__ | RelationshipManager initialized.
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5628 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5678 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5720 | __init__ | NewsFetcher initialized.
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5905 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5983 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:5996 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:8304 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:8305 | <module> | Operating system: win32
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:8309 | <module> | CUDA not available, using CPU
2025-08-20 18:53:53 | INFO     | 4960 | 33528 | app.py:7915 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 18:53:54 | INFO     | 4960 | 33528 | app.py:8159 | create_gradio_interface | Gradio interface created.
2025-08-20 18:53:54 | INFO     | 4960 | 33528 | app.py:8332 | <module> | Launching Gradio interface...
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:1718 | verify_user | Attempting login for username: dha
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:1734 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:3016 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:00 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:00 | WARNING  | 4960 | 32940 | app.py:891 | get_connection | DB transaction rolled back due to error.
2025-08-20 18:55:00 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:00 | INFO     | 4960 | 32940 | app.py:7798 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya Dhananjay! What's on your mind?' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:8061 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:7238 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:6028 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:6061 | _analyze_and_update_user_style | Insufficient user messages (10/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1260 | log_chat_message | Chat message logged (ID: 21) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:5324 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1347 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:3348 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:3376 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:462 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:467 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0007340908050537109, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 11, "errors": 0}
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:3455 | _load_and_index_user_memories | Successfully loaded and indexed 11/11 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1642 | record_memory_access | Recorded access attempt for 3 memory entries (requested: 3).
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:4089 | recall_related_memories | Recalled 3 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 3 candidates)
2025-08-20 18:55:03 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:03 | WARNING  | 4960 | 32940 | app.py:891 | get_connection | DB transaction rolled back due to error.
2025-08-20 18:55:03 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1888 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1915 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 18:55:03 | INFO     | 4960 | 32940 | app.py:1979 | _load_facts_from_db | Loaded 4 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:04 | INFO     | 4960 | 32940 | app.py:6467 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:55:12 | ERROR    | 4960 | 32940 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:7389 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! How are you doing? It feels like ages since we last chatted – or maybe it was just a f...'
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:1260 | log_chat_message | Chat message logged (ID: 22) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:55:13 | WARNING  | 4960 | 32940 | app.py:7639 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 12, "memory_type": "short_term"}
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:3612 | _store_single_memory | Stored memory successfully (DB ID: 12, Store Idx: 11, FAISS Idx: 11, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:3512 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:13 | WARNING  | 4960 | 32940 | app.py:5561 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:5603 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.62 -> 0.63 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:55:13 | INFO     | 4960 | 32940 | app.py:7651 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 9.71s)
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:8061 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'so where do i study and in which branch...'
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:7238 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'so where do i study and in which branch...'
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:6028 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:6061 | _analyze_and_update_user_style | Insufficient user messages (11/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1260 | log_chat_message | Chat message logged (ID: 23) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1347 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:1642 | record_memory_access | Recorded access attempt for 6 memory entries (requested: 6).
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:4089 | recall_related_memories | Recalled 6 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 6 candidates)
2025-08-20 18:55:41 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:41 | WARNING  | 4960 | 32940 | app.py:891 | get_connection | DB transaction rolled back due to error.
2025-08-20 18:55:41 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:41 | INFO     | 4960 | 32940 | app.py:6467 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:55:54 | ERROR    | 4960 | 32940 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:55:54 | INFO     | 4960 | 32940 | app.py:7389 | generate_personalized_response | Final Humanized Response: 'Oh hey Dhananjay! That's a big question, isn't it? Choosing where to study and what to study... it c...'
2025-08-20 18:55:54 | INFO     | 4960 | 32940 | app.py:1260 | log_chat_message | Chat message logged (ID: 24) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:7213 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.24, "memory_type": "short_term", "text_length": 39, "emotion": "curiosity"}
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 13, "memory_type": "short_term"}
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:3612 | _store_single_memory | Stored memory successfully (DB ID: 13, Store Idx: 12, FAISS Idx: 12, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.59, "memory_type": "micro_detail", "text_length": 36, "emotion": "curiosity"}
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 14, "memory_type": "micro_detail"}
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:3612 | _store_single_memory | Stored memory successfully (DB ID: 14, Store Idx: 13, FAISS Idx: 13, Type: micro_detail) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:3512 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:5603 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.63 -> 0.75 (Inc: 0.118 [Vuln:0.045, Len:0.063, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:2502 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:55:56 | INFO     | 4960 | 32940 | app.py:7651 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 15.86s)
2025-08-20 18:56:00 | INFO     | 4960 | 32940 | app.py:8139 | handle_logout | User logging out.
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:1718 | verify_user | Attempting login for username: dha
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:1734 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:3016 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:56:05 | ERROR    | 4960 | 34100 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:56:05 | WARNING  | 4960 | 34100 | app.py:891 | get_connection | DB transaction rolled back due to error.
2025-08-20 18:56:05 | ERROR    | 4960 | 34100 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:56:05 | INFO     | 4960 | 34100 | app.py:7798 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hello Dhananjay! How can I help?' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:8061 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:4607 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:4625 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 14
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:7238 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:6028 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:6061 | _analyze_and_update_user_style | Insufficient user messages (12/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1260 | log_chat_message | Chat message logged (ID: 25) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1347 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:1642 | record_memory_access | Recorded access attempt for 4 memory entries (requested: 4).
2025-08-20 18:58:06 | INFO     | 4960 | 34100 | app.py:4089 | recall_related_memories | Recalled 4 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 4 candidates)
2025-08-20 18:58:06 | ERROR    | 4960 | 34100 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:58:06 | WARNING  | 4960 | 34100 | app.py:891 | get_connection | DB transaction rolled back due to error.
2025-08-20 18:58:06 | ERROR    | 4960 | 34100 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:58:07 | INFO     | 4960 | 34100 | app.py:6467 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 18:58:12 | ERROR    | 4960 | 34100 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:7389 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! It's so nice to...oh right hear from you. How are you doing? It feels like we were jus...'
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:1260 | log_chat_message | Chat message logged (ID: 26) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 18:58:12 | WARNING  | 4960 | 34100 | app.py:7639 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 15, "memory_type": "short_term"}
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:3612 | _store_single_memory | Stored memory successfully (DB ID: 15, Store Idx: 14, FAISS Idx: 14, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:3512 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 18:58:12 | WARNING  | 4960 | 34100 | app.py:5561 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:5603 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.75 -> 0.76 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 18:58:12 | INFO     | 4960 | 34100 | app.py:7651 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 6.62s)
2025-08-20 19:00:43 | INFO     | 4960 | 33528 | app.py:8357 | <module> | Gradio application closed.
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:473 | setup_logging | Structured logging configured for production environment
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:487 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:8301 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:797 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:841 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:912 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:1095 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:1687 | __init__ | AuthenticationManager initialized.
2025-08-20 19:01:30 | INFO     | 21380 | 26340 | app.py:5139 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 19:01:33 | INFO     | 21380 | 26340 | app.py:5156 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 19:01:36 | INFO     | 21380 | 26340 | app.py:5166 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 19:01:36 | INFO     | 21380 | 26340 | app.py:526 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 19:01:36 | INFO     | 21380 | 26340 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 19:01:36 | INFO     | 21380 | 26340 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:550 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:3316 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:1847 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:1780 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5324 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5562 | __init__ | RelationshipManager initialized.
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5638 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5688 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5730 | __init__ | NewsFetcher initialized.
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5915 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:5993 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:6006 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:8314 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:8315 | <module> | Operating system: win32
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:8319 | <module> | CUDA not available, using CPU
2025-08-20 19:01:41 | INFO     | 21380 | 26340 | app.py:7925 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 19:01:42 | INFO     | 21380 | 26340 | app.py:8169 | create_gradio_interface | Gradio interface created.
2025-08-20 19:01:42 | INFO     | 21380 | 26340 | app.py:8342 | <module> | Launching Gradio interface...
2025-08-20 19:01:55 | INFO     | 21380 | 19712 | app.py:1719 | verify_user | Attempting login for username: dha
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:1735 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:3017 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:01:56 | INFO     | 21380 | 19712 | app.py:7808 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hello Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:8071 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:4616 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:4634 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 15
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:7248 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:6038 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:6071 | _analyze_and_update_user_style | Insufficient user messages (13/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 19:02:00 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 27) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:5334 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1348 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:3357 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:3385 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:462 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:467 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0005033016204833984, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 15, "errors": 0}
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:3464 | _load_and_index_user_memories | Successfully loaded and indexed 15/15 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1643 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:4098 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 5 candidates)
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1889 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1916 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:1980 | _load_facts_from_db | Loaded 4 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:01 | INFO     | 21380 | 19712 | app.py:6477 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:02:11 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:7399 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! feels like ages since we last chatted, even though it probably wasn't that long. How a...'
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 28) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:02:11 | WARNING  | 21380 | 19712 | app.py:7649 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 16, "memory_type": "short_term"}
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:3621 | _store_single_memory | Stored memory successfully (DB ID: 16, Store Idx: 15, FAISS Idx: 15, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:3521 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:11 | WARNING  | 21380 | 19712 | app.py:5571 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:5613 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.76 -> 0.77 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:02:11 | INFO     | 21380 | 19712 | app.py:7661 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 10.27s)
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:8071 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'what is day today...'
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:4616 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:4634 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 16
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:7248 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'what is day today...'
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:6038 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:6071 | _analyze_and_update_user_style | Insufficient user messages (14/15) for style analysis (User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f). Will try later.
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 29) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:1348 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:02:32 | INFO     | 21380 | 19712 | app.py:4098 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 19:02:33 | INFO     | 21380 | 19712 | app.py:6477 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:02:42 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:02:42 | INFO     | 21380 | 19712 | app.py:7399 | generate_personalized_response | Final Humanized Response: 'Oh hey Dhananjay! It's Wednesday, the 20th of August. Hope you're having a good one so far? Um, anyt...'
2025-08-20 19:02:42 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 30) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:7223 | _summarize_interaction | Interaction summarized. Vuln=0.00, Reci=False, Insights=1
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.18, "memory_type": "short_term", "text_length": 17, "emotion": "realization"}
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 17, "memory_type": "short_term"}
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:3621 | _store_single_memory | Stored memory successfully (DB ID: 17, Store Idx: 16, FAISS Idx: 16, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:3521 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:02:43 | INFO     | 21380 | 19712 | app.py:5613 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.77 -> 0.81 (Inc: 0.049 [Vuln:0.000, Len:0.039, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:02:44 | INFO     | 21380 | 19712 | app.py:2503 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:02:44 | INFO     | 21380 | 19712 | app.py:7661 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 12.02s)
2025-08-20 19:03:13 | INFO     | 21380 | 19712 | app.py:8149 | handle_logout | User logging out.
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:1719 | verify_user | Attempting login for username: dha
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:1735 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:3017 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:18 | INFO     | 21380 | 19712 | app.py:7808 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hello Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:8071 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:4616 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:4634 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 17
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:7248 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:6038 | _analyze_and_update_user_style | Initial user style analysis for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: avg_msg_length, emoji_frequency, question_rate, formality_score, last_style_analysis_ts, last_active
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5471 | adapt_from_interaction_style | Adapted trait 'formality' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f based on style 'formality_score': 0.300 -> 0.390
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5471 | adapt_from_interaction_style | Adapted trait 'warmth' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f based on style 'emoji_frequency': 0.700 -> 0.640
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5471 | adapt_from_interaction_style | Adapted trait 'curiosity' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f based on style 'question_rate': 0.600 -> 0.570
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:6136 | _analyze_and_update_user_style | Updated style metrics for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {'avg_msg_length': 16.3, 'emoji_frequency': 0.0, 'question_rate': 0.0, 'formality_score': np.float64(0.9), 'last_style_analysis_ts': '2025-08-20T13:33:21.091742+00:00', 'last_active': '2025-08-20T13:33:21.091783+00:00'}
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 31) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1348 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5424 | adapt_from_feedback | Adapted trait 'warmth' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.640 -> 0.641 (Adj: 0.000, Decay: -0.001)
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5424 | adapt_from_feedback | Adapted trait 'curiosity' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.570 -> 0.570 (Adj: 0.000, Decay: -0.000)
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:5424 | adapt_from_feedback | Adapted trait 'formality' for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.390 -> 0.389 (Adj: 0.000, Decay: 0.001)
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:1643 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 19:03:21 | INFO     | 21380 | 19712 | app.py:4098 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 6 candidates)
2025-08-20 19:03:22 | INFO     | 21380 | 19712 | app.py:6477 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:03:29 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:7399 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! It feels like we were just chatting, doesn't it? How are you doing today? Hope you're ...'
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:1260 | log_chat_message | Chat message logged (ID: 32) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:03:29 | WARNING  | 21380 | 19712 | app.py:7649 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:462 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:467 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 18, "memory_type": "short_term"}
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:3621 | _store_single_memory | Stored memory successfully (DB ID: 18, Store Idx: 17, FAISS Idx: 17, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:3521 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:1206 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:03:29 | WARNING  | 21380 | 19712 | app.py:5571 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:1164 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:5613 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.81 -> 0.82 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:03:29 | INFO     | 21380 | 19712 | app.py:7661 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 8.85s)
2025-08-20 19:20:54 | INFO     | 21380 | 26340 | app.py:8367 | <module> | Gradio application closed.
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:511 | setup_logging | Structured logging configured for production environment
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:525 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:8755 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:835 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:879 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:950 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:1133 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:1725 | __init__ | AuthenticationManager initialized.
2025-08-20 19:22:36 | INFO     | 37584 | 31848 | app.py:5552 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 19:22:40 | INFO     | 37584 | 31848 | app.py:5569 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 19:22:43 | INFO     | 37584 | 31848 | app.py:5579 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 19:22:43 | INFO     | 37584 | 31848 | app.py:564 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 19:22:43 | INFO     | 37584 | 31848 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 19:22:43 | INFO     | 37584 | 31848 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:588 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:3729 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:1885 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:1818 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:5737 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:5975 | __init__ | RelationshipManager initialized.
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6051 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6101 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6143 | __init__ | NewsFetcher initialized.
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6328 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:3066 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:3111 | _load_global_knowledge | Initialized empty global knowledge index
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6409 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:6422 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8768 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8769 | <module> | Operating system: win32
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8773 | <module> | CUDA not available, using CPU
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8379 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8623 | create_gradio_interface | Gradio interface created.
2025-08-20 19:22:48 | INFO     | 37584 | 31848 | app.py:8796 | <module> | Launching Gradio interface...
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:1757 | verify_user | Attempting login for username: dha
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:1773 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:3430 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:13 | INFO     | 37584 | 31396 | app.py:8238 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:8525 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:3148 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:7670 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 33) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:5747 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1386 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:3770 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:3798 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:500 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:505 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0009400844573974609, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 18, "errors": 0}
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:3877 | _load_and_index_user_memories | Successfully loaded and indexed 18/18 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1681 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:4511 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 7 candidates)
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1927 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:1954 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:23:16 | INFO     | 37584 | 31396 | app.py:2018 | _load_facts_from_db | Loaded 4 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:23:17 | INFO     | 37584 | 31396 | app.py:6899 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:23:25 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:7821 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! It feels like we've been chatting quite a bit lately, hasn't it? Hope you're doing oka...'
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 34) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:23:25 | WARNING  | 37584 | 31396 | app.py:8071 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:500 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:505 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 19, "memory_type": "short_term"}
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:4034 | _store_single_memory | Stored memory successfully (DB ID: 19, Store Idx: 18, FAISS Idx: 18, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:3934 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:25 | WARNING  | 37584 | 31396 | app.py:5984 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:1202 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:6026 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.82 -> 0.83 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:23:25 | INFO     | 37584 | 31396 | app.py:8083 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 9.60s)
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:8525 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'do you know bangalore coffee is good...'
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:3239 | _store_global_knowledge | Stored new global knowledge: General knowledge: is good...
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:3148 | extract_and_store_knowledge | Extracted 1 general facts, 0 personal details
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:8138 | handle_user_message | Extracted 1 general facts from user message
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:7670 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'do you know bangalore coffee is good...'
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 35) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:1386 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:4511 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 19:23:49 | INFO     | 37584 | 31396 | app.py:6899 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:23:59 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:23:59 | INFO     | 37584 | 31396 | app.py:7821 | generate_personalized_response | Final Humanized Response: 'Oh, absolutely! Bangalore coffee… it's *amazing*, isn't it? I love that... There's something so comf...'
2025-08-20 19:23:59 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 36) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:7645 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:500 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.18, "memory_type": "short_term", "text_length": 36, "emotion": "curiosity"}
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:505 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 20, "memory_type": "short_term"}
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:4034 | _store_single_memory | Stored memory successfully (DB ID: 20, Store Idx: 19, FAISS Idx: 19, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:3934 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:1202 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:6026 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.83 -> 0.92 (Inc: 0.086 [Vuln:0.022, Len:0.053, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:2541 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:24:01 | INFO     | 37584 | 31396 | app.py:8083 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 12.82s)
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:8525 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'where do i study...'
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:3148 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:7670 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'where do i study...'
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 37) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1386 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:1681 | record_memory_access | Recorded access attempt for 7 memory entries (requested: 7).
2025-08-20 19:29:20 | INFO     | 37584 | 31396 | app.py:4511 | recall_related_memories | Recalled 7 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 6 candidates)
2025-08-20 19:29:21 | INFO     | 37584 | 31396 | app.py:6899 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:29:36 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:29:36 | INFO     | 37584 | 31396 | app.py:7821 | generate_personalized_response | Final Humanized Response: 'Mmmm Oh, hey Dhananjay! That's a big question, isn't it? You've been thinking about this a lot latel...'
2025-08-20 19:29:36 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 38) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:7645 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:500 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.21, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:505 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 21, "memory_type": "short_term"}
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:4034 | _store_single_memory | Stored memory successfully (DB ID: 21, Store Idx: 20, FAISS Idx: 20, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:3934 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:1202 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:29:37 | INFO     | 37584 | 31396 | app.py:6026 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 0.92 -> 1.03 (Inc: 0.108 [Vuln:0.045, Len:0.053, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:29:38 | INFO     | 37584 | 31396 | app.py:2541 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:29:38 | INFO     | 37584 | 31396 | app.py:8083 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 17.78s)
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:8525 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'which branch do i stdy...'
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:3148 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:7670 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'which branch do i stdy...'
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 39) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1386 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:1681 | record_memory_access | Recorded access attempt for 4 memory entries (requested: 4).
2025-08-20 19:30:48 | INFO     | 37584 | 31396 | app.py:4511 | recall_related_memories | Recalled 4 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 3 candidates)
2025-08-20 19:30:49 | INFO     | 37584 | 31396 | app.py:6899 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:31:02 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:31:02 | INFO     | 37584 | 31396 | app.py:7821 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay, that's a BIG question! It feels like choosing a path, doesn't it? A bit like standing...'
2025-08-20 19:31:02 | INFO     | 37584 | 31396 | app.py:1298 | log_chat_message | Chat message logged (ID: 40) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:31:03 | INFO     | 37584 | 31396 | app.py:7645 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 19:31:03 | INFO     | 37584 | 31396 | app.py:500 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.23, "memory_type": "short_term", "text_length": 22, "emotion": "confusion"}
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:505 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 22, "memory_type": "short_term"}
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:4034 | _store_single_memory | Stored memory successfully (DB ID: 22, Store Idx: 21, FAISS Idx: 21, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:3934 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:1244 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:1202 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:6026 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.03 -> 1.13 (Inc: 0.099 [Vuln:0.045, Len:0.044, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:2541 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:31:04 | INFO     | 37584 | 31396 | app.py:8083 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 16.39s)
2025-08-20 19:31:20 | INFO     | 37584 | 31848 | app.py:8821 | <module> | Gradio application closed.
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:533 | setup_logging | Structured logging configured for production environment
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:547 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:8820 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:857 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:901 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:972 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:1155 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:1747 | __init__ | AuthenticationManager initialized.
2025-08-20 19:32:36 | INFO     | 26716 | 25804 | app.py:5574 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 19:32:38 | INFO     | 26716 | 25804 | app.py:5591 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 19:32:41 | INFO     | 26716 | 25804 | app.py:5601 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 19:32:41 | INFO     | 26716 | 25804 | app.py:586 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 19:32:41 | INFO     | 26716 | 25804 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 19:32:41 | INFO     | 26716 | 25804 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:610 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:3751 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:1907 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:1840 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:5759 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:5997 | __init__ | RelationshipManager initialized.
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6073 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6123 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6165 | __init__ | NewsFetcher initialized.
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6350 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:3088 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:3129 | _load_global_knowledge | Loaded 1 global knowledge entries
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6431 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:6444 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8833 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8834 | <module> | Operating system: win32
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8838 | <module> | CUDA not available, using CPU
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8444 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8688 | create_gradio_interface | Gradio interface created.
2025-08-20 19:32:46 | INFO     | 26716 | 25804 | app.py:8861 | <module> | Launching Gradio interface...
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:1779 | verify_user | Attempting login for username: dha
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:1795 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:3452 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:24 | INFO     | 26716 | 36404 | app.py:8251 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:8590 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:3170 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:7683 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 41) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:5769 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1408 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:3792 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:3820 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:522 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:527 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0008351802825927734, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 22, "errors": 0}
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:3899 | _load_and_index_user_memories | Successfully loaded and indexed 22/22 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1703 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:4533 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 8 candidates)
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1949 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:1976 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:34:27 | INFO     | 26716 | 36404 | app.py:2040 | _load_facts_from_db | Loaded 4 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:34:28 | INFO     | 26716 | 36404 | app.py:6921 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:7834 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! tbh How are you doing? It feels like it's been a while – hope everything's going well....'
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 42) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:34:37 | WARNING  | 26716 | 36404 | app.py:8084 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:522 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:527 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 23, "memory_type": "short_term"}
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:4056 | _store_single_memory | Stored memory successfully (DB ID: 23, Store Idx: 22, FAISS Idx: 22, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:3956 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:34:37 | WARNING  | 26716 | 36404 | app.py:6006 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:1224 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:6048 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.13 -> 1.14 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:34:37 | INFO     | 26716 | 36404 | app.py:8096 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 9.86s)
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:8590 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'i study in bit mesr aproduction enginerring...'
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:5051 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:5069 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 23
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:3261 | _store_global_knowledge | Stored new global knowledge: General knowledge: in bit mesr aproduction enginer...
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:3170 | extract_and_store_knowledge | Extracted 1 general facts, 0 personal details
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:8151 | handle_user_message | Extracted 1 general facts from user message
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:7683 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'i study in bit mesr aproduction enginerring...'
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 43) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1408 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:1703 | record_memory_access | Recorded access attempt for 9 memory entries (requested: 9).
2025-08-20 19:36:50 | INFO     | 26716 | 36404 | app.py:4533 | recall_related_memories | Recalled 9 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 8 candidates)
2025-08-20 19:36:51 | INFO     | 26716 | 36404 | app.py:6921 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:37:01 | INFO     | 26716 | 36404 | app.py:7834 | generate_personalized_response | Final Humanized Response: 'Whoa Oh, wow, BIT Mesra! Production Engineering, huh? That sounds…intense. I remember you mentioning...'
2025-08-20 19:37:01 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 44) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:7658 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:522 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.17, "memory_type": "short_term", "text_length": 43, "emotion": "curiosity"}
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:527 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 24, "memory_type": "short_term"}
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:4056 | _store_single_memory | Stored memory successfully (DB ID: 24, Store Idx: 23, FAISS Idx: 23, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:522 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.52, "memory_type": "micro_detail", "text_length": 52, "emotion": "curiosity"}
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:527 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 25, "memory_type": "micro_detail"}
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:4056 | _store_single_memory | Stored memory successfully (DB ID: 25, Store Idx: 24, FAISS Idx: 24, Type: micro_detail) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:3956 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:1224 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:37:02 | INFO     | 26716 | 36404 | app.py:6048 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.14 -> 1.21 (Inc: 0.070 [Vuln:0.022, Len:0.037, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:37:03 | INFO     | 26716 | 36404 | app.py:2261 | add_fact | Added fact for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: The user studies Production Engineering at BIT Mes... with ID 430ac3bb-adb8-4926-b7de-6c39453c4b11
2025-08-20 19:37:03 | INFO     | 26716 | 36404 | app.py:2563 | extract_facts_from_message | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:37:03 | INFO     | 26716 | 36404 | app.py:8080 | generate_personalized_response | Extracted 1 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:37:03 | INFO     | 26716 | 36404 | app.py:8096 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 13.04s)
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:8590 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'in whuch city cani fidn ebst coffee...'
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:3261 | _store_global_knowledge | Stored new global knowledge: General knowledge: cani fidn ebst coffee...
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:3170 | extract_and_store_knowledge | Extracted 1 general facts, 0 personal details
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:8151 | handle_user_message | Extracted 1 general facts from user message
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:7683 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'in whuch city cani fidn ebst coffee...'
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:38:10 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 45) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:1408 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:1703 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:4533 | recall_related_memories | Recalled 1 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 1 candidates)
2025-08-20 19:38:11 | INFO     | 26716 | 36404 | app.py:6921 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:38:24 | INFO     | 26716 | 36404 | app.py:7834 | generate_personalized_response | Final Humanized Response: 'Oh, uh... wow, best coffee, huh? That's exciting! You know, I've been meaning to explore more coffee...'
2025-08-20 19:38:24 | INFO     | 26716 | 36404 | app.py:1320 | log_chat_message | Chat message logged (ID: 46) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:7658 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:522 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.16, "memory_type": "short_term", "text_length": 35, "emotion": "excitement"}
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:527 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 26, "memory_type": "short_term"}
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:4056 | _store_single_memory | Stored memory successfully (DB ID: 26, Store Idx: 25, FAISS Idx: 25, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:3956 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:1266 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:1224 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:38:25 | INFO     | 26716 | 36404 | app.py:6048 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.21 -> 1.30 (Inc: 0.089 [Vuln:0.022, Len:0.057, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:38:26 | INFO     | 26716 | 36404 | app.py:2563 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:38:26 | INFO     | 26716 | 36404 | app.py:8096 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 15.22s)
2025-08-20 19:42:38 | INFO     | 26716 | 25804 | app.py:8886 | <module> | Gradio application closed.
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:535 | setup_logging | Structured logging configured for production environment
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:549 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:9536 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:859 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:903 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:974 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:1157 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:1749 | __init__ | AuthenticationManager initialized.
2025-08-20 19:51:49 | INFO     | 30380 | 30384 | app.py:6184 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 19:51:51 | INFO     | 30380 | 30384 | app.py:6201 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 19:51:54 | INFO     | 30380 | 30384 | app.py:6211 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 19:51:54 | INFO     | 30380 | 30384 | app.py:588 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 19:51:54 | INFO     | 30380 | 30384 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 19:51:54 | INFO     | 30380 | 30384 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:612 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:4361 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:1909 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:1842 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6369 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6607 | __init__ | RelationshipManager initialized.
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6683 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6733 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6775 | __init__ | NewsFetcher initialized.
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:6960 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:3698 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:3739 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:3141 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:7044 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:7057 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:9549 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:9550 | <module> | Operating system: win32
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:9554 | <module> | CUDA not available, using CPU
2025-08-20 19:51:58 | INFO     | 30380 | 30384 | app.py:9160 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 19:51:59 | INFO     | 30380 | 30384 | app.py:9404 | create_gradio_interface | Gradio interface created.
2025-08-20 19:51:59 | INFO     | 30380 | 30384 | app.py:9577 | <module> | Launching Gradio interface...
2025-08-20 19:53:06 | INFO     | 30380 | 31348 | app.py:9384 | handle_logout | User logging out.
2025-08-20 19:53:11 | INFO     | 30380 | 31348 | app.py:1781 | verify_user | Attempting login for username: dha
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:1797 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:4062 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:12 | INFO     | 30380 | 31348 | app.py:8886 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:9306 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:3780 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:3160 | analyze_and_track_interests | Detected 0 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:8303 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:14 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 47) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:6379 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1410 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:4402 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:4430 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:524 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:529 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0007750988006591797, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 26, "errors": 0}
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:4509 | _load_and_index_user_memories | Successfully loaded and indexed 26/26 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1705 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:5143 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 9 candidates)
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1951 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:1978 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:2042 | _load_facts_from_db | Loaded 5 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:53:15 | INFO     | 30380 | 31348 | app.py:7541 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:8454 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! It feels like ages How are you doing? Everything alright? actually 😊...'
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 48) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:53:22 | WARNING  | 30380 | 31348 | app.py:8704 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:524 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:529 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 27, "memory_type": "short_term"}
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:4666 | _store_single_memory | Stored memory successfully (DB ID: 27, Store Idx: 26, FAISS Idx: 26, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:4566 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:53:22 | WARNING  | 30380 | 31348 | app.py:6616 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:1226 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:6658 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.30 -> 1.31 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:53:22 | INFO     | 30380 | 31348 | app.py:8716 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 7.48s)
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:9306 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'when did we last chat...'
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:3780 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:3160 | analyze_and_track_interests | Detected 0 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:8303 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'when did we last chat...'
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 49) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:1410 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:54:41 | INFO     | 30380 | 31348 | app.py:5143 | recall_related_memories | Recalled 0 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 0 candidates)
2025-08-20 19:54:42 | INFO     | 30380 | 31348 | app.py:7541 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:54:49 | INFO     | 30380 | 31348 | app.py:8454 | generate_personalized_response | Final Humanized Response: 'Oh, hey Dhananjay! That's a funny question! Why do you want to know? 🤔 It feels like just a little w...'
2025-08-20 19:54:49 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 50) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:8278 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:524 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.19, "memory_type": "short_term", "text_length": 21, "emotion": "confusion"}
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:529 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 28, "memory_type": "short_term"}
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:4666 | _store_single_memory | Stored memory successfully (DB ID: 28, Store Idx: 27, FAISS Idx: 27, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:4566 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:1226 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:54:50 | INFO     | 30380 | 31348 | app.py:6658 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.31 -> 1.40 (Inc: 0.092 [Vuln:0.022, Len:0.060, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:54:52 | INFO     | 30380 | 31348 | app.py:2565 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:54:52 | INFO     | 30380 | 31348 | app.py:8716 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 10.99s)
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:9306 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'where do i study...'
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:3780 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:3160 | analyze_and_track_interests | Detected 1 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:8779 | handle_user_message | Detected interests in 1 categories
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:8303 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'where do i study...'
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 51) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1410 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:1705 | record_memory_access | Recorded access attempt for 9 memory entries (requested: 9).
2025-08-20 19:56:57 | INFO     | 30380 | 31348 | app.py:5143 | recall_related_memories | Recalled 9 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 8 candidates)
2025-08-20 19:56:58 | INFO     | 30380 | 31348 | app.py:7541 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 19:57:08 | INFO     | 30380 | 31348 | app.py:8454 | generate_personalized_response | Final Humanized Response: 'Oh, hey Dhananjay! That's a great question! You know,... So, where *are* you studying? And, um, is i...'
2025-08-20 19:57:08 | INFO     | 30380 | 31348 | app.py:1322 | log_chat_message | Chat message logged (ID: 52) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:8278 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:524 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.21, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:529 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 29, "memory_type": "short_term"}
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:4666 | _store_single_memory | Stored memory successfully (DB ID: 29, Store Idx: 28, FAISS Idx: 28, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:4566 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:1268 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:1226 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 19:57:09 | INFO     | 30380 | 31348 | app.py:6658 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.40 -> 1.50 (Inc: 0.106 [Vuln:0.045, Len:0.051, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 19:57:10 | INFO     | 30380 | 31348 | app.py:2565 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 19:57:10 | INFO     | 30380 | 31348 | app.py:8716 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 12.84s)
2025-08-20 20:02:28 | INFO     | 30380 | 30384 | app.py:9602 | <module> | Gradio application closed.
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:538 | setup_logging | Structured logging configured for production environment
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:552 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:9659 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:862 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:906 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:977 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:1160 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:1752 | __init__ | AuthenticationManager initialized.
2025-08-20 20:03:03 | INFO     | 30736 | 7688 | app.py:6187 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 20:03:07 | INFO     | 30736 | 7688 | app.py:6204 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 20:03:10 | INFO     | 30736 | 7688 | app.py:6214 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 20:03:10 | INFO     | 30736 | 7688 | app.py:591 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 20:03:10 | INFO     | 30736 | 7688 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 20:03:10 | INFO     | 30736 | 7688 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:615 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:4364 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:1912 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:1845 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6372 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6610 | __init__ | RelationshipManager initialized.
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6686 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6736 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6778 | __init__ | NewsFetcher initialized.
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:6963 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:3701 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:3742 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:3144 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:7047 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:7060 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9672 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9673 | <module> | Operating system: win32
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9677 | <module> | CUDA not available, using CPU
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9283 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9527 | create_gradio_interface | Gradio interface created.
2025-08-20 20:03:15 | INFO     | 30736 | 7688 | app.py:9700 | <module> | Launching Gradio interface...
2025-08-20 20:03:46 | INFO     | 30736 | 24244 | app.py:1784 | verify_user | Attempting login for username: dha
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:1800 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:4065 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:48 | INFO     | 30736 | 24244 | app.py:8903 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Heya Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:9429 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:5664 | consolidate_memories | Consolidating memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (max_short_term: 200)
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:5682 | consolidate_memories | No need to consolidate memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'. Short-term count: 29
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:3783 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:3163 | analyze_and_track_interests | Detected 0 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:8320 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:51 | INFO     | 30736 | 20400 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:1325 | log_chat_message | Chat message logged (ID: 53) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:6382 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:1413 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:4405 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:4433 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:527 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:532 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0013935565948486328, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 29, "errors": 0}
2025-08-20 20:03:52 | INFO     | 30736 | 20400 | app.py:4512 | _load_and_index_user_memories | Successfully loaded and indexed 29/29 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 20:03:53 | INFO     | 30736 | 20400 | app.py:1708 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 20:03:53 | INFO     | 30736 | 20400 | app.py:5146 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 10 candidates)
2025-08-20 20:03:53 | ERROR    | 30736 | 20400 | app.py:8747 | generate_personalized_response | CRITICAL Unhandled error during response generation for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'ChatManager' object has no attribute '_get_direct_fact_recall'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 8400, in generate_personalized_response
    prompt = self._construct_llm_prompt(
        user_message_text=user_message_text, user_id=user_id, user_profile=user_profile,
    ...<2 lines>...
        style_params=style_params, _chat_history=current_chat_history # Pass current history before user message
    )
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 7382, in _construct_llm_prompt
    direct_fact_recall = self._get_direct_fact_recall(user_id, user_message_text)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ChatManager' object has no attribute '_get_direct_fact_recall'
2025-08-20 20:03:53 | INFO     | 30736 | 20400 | app.py:1325 | log_chat_message | Chat message logged (ID: 54) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 20:05:57 | INFO     | 30736 | 7688 | app.py:9725 | <module> | Gradio application closed.
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:538 | setup_logging | Structured logging configured for production environment
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:552 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:9659 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:862 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:906 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:977 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:1160 | setup_database | Database schema setup/verification complete (V4).
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:1752 | __init__ | AuthenticationManager initialized.
2025-08-20 20:07:34 | INFO     | 8760 | 33688 | app.py:6187 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-20 20:07:37 | INFO     | 8760 | 33688 | app.py:6204 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-20 20:07:40 | INFO     | 8760 | 33688 | app.py:6214 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-20 20:07:40 | INFO     | 8760 | 33688 | app.py:591 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-20 20:07:40 | INFO     | 8760 | 33688 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-20 20:07:40 | INFO     | 8760 | 33688 | SentenceTransformer.py:227 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:615 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:4364 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:1912 | _init_database_schema | Knowledge graph database schema initialized
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:1845 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6372 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6610 | __init__ | RelationshipManager initialized.
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6686 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6736 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6778 | __init__ | NewsFetcher initialized.
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:6963 | __init__ | FestivalTracker initialized (using static list).
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:3701 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:3742 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:3144 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:7047 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:7060 | __init__ | ChatManager initialized successfully (V4).
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:9672 | <module> | Python version: 3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:9673 | <module> | Operating system: win32
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:9677 | <module> | CUDA not available, using CPU
2025-08-20 20:07:44 | INFO     | 8760 | 33688 | app.py:9283 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-20 20:07:45 | INFO     | 8760 | 33688 | app.py:9527 | create_gradio_interface | Gradio interface created.
2025-08-20 20:07:45 | INFO     | 8760 | 33688 | app.py:9700 | <module> | Launching Gradio interface...
2025-08-20 20:08:31 | INFO     | 8760 | 22892 | app.py:1784 | verify_user | Attempting login for username: dha
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:1800 | verify_user | User 'dha' (ID: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f) logged in successfully.
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:4065 | start_session | Started session for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, gap since last: 0.0 hours
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:32 | INFO     | 8760 | 22892 | app.py:8903 | get_initial_greeting | Generated time-aware greeting for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'Hey Dhananjay! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:9429 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'hi...'
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:3783 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:3163 | analyze_and_track_interests | Detected 0 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:8320 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'hi...'
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:34 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 55) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:6382 | _get_user_traits | Initialized traits for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f from baseline.
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1413 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:4405 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:4433 | _load_and_index_user_memories | Loading and indexing memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_load", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "limit": 250, "memory_type": "all"}
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0009582042694091797, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "count": 29, "errors": 0}
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:4512 | _load_and_index_user_memories | Successfully loaded and indexed 29/29 memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1708 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:5146 | recall_related_memories | Recalled 5 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 10 candidates)
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1954 | _get_or_create_fact_index | Initializing FAISS index for facts of user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:1981 | _load_facts_from_db | Loading and indexing facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' from DB.
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:2045 | _load_facts_from_db | Loaded 5 facts for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:08:35 | INFO     | 8760 | 22892 | app.py:7558 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:8471 | generate_personalized_response | Final Humanized Response: 'Hi Dhananjay! It feels like ages since we last chatted – or maybe it was just this morning? Hope you...'
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 56) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 20:08:45 | WARNING  | 8760 | 22892 | app.py:8721 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 30, "memory_type": "short_term"}
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:4669 | _store_single_memory | Stored memory successfully (DB ID: 30, Store Idx: 29, FAISS Idx: 29, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:4569 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:08:45 | WARNING  | 8760 | 22892 | app.py:6619 | update_relationship_depth | No interaction summary provided for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, applying decay only.
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:1229 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:6661 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.50 -> 1.51 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 20:08:45 | INFO     | 8760 | 22892 | app.py:8733 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 11.14s)
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:9429 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'when did we ast chat...'
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:3783 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:3163 | analyze_and_track_interests | Detected 0 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:8320 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'when did we ast chat...'
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 57) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1413 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:1708 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-08-20 20:09:01 | INFO     | 8760 | 22892 | app.py:5146 | recall_related_memories | Recalled 1 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 1 candidates)
2025-08-20 20:09:02 | INFO     | 8760 | 22892 | app.py:7558 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 20:09:08 | INFO     | 8760 | 22892 | app.py:8471 | generate_personalized_response | Final Humanized Response: 'Oh hey Dhananjay! Just a few minutes ago, actually. Time flies when we're chatting, doesn't it? 😊 Yo...'
2025-08-20 20:09:08 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 58) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:8295 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.19, "memory_type": "short_term", "text_length": 20, "emotion": "curiosity"}
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 31, "memory_type": "short_term"}
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:4669 | _store_single_memory | Stored memory successfully (DB ID: 31, Store Idx: 30, FAISS Idx: 30, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:4569 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:1229 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:6661 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.51 -> 1.60 (Inc: 0.082 [Vuln:0.022, Len:0.050, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:2568 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:10 | INFO     | 8760 | 22892 | app.py:8733 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 9.52s)
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:9429 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'where do i study...'
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:3783 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:3163 | analyze_and_track_interests | Detected 1 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:8796 | handle_user_message | Detected interests in 1 categories
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:8320 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'where do i study...'
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 59) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1413 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:1708 | record_memory_access | Recorded access attempt for 10 memory entries (requested: 10).
2025-08-20 20:09:15 | INFO     | 8760 | 22892 | app.py:5146 | recall_related_memories | Recalled 10 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 9 candidates)
2025-08-20 20:09:16 | INFO     | 8760 | 22892 | app.py:7558 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 20:09:30 | INFO     | 8760 | 22892 | app.py:8471 | generate_personalized_response | Final Humanized Response: 'Hey Dhananjay! That's a big question, isn't it? Where you study... basically it's such a huge part o...'
2025-08-20 20:09:30 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 60) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:8295 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.21, "memory_type": "short_term", "text_length": 16, "emotion": "confusion"}
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 32, "memory_type": "short_term"}
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:4669 | _store_single_memory | Stored memory successfully (DB ID: 32, Store Idx: 31, FAISS Idx: 31, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:4569 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:1229 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 20:09:32 | INFO     | 8760 | 22892 | app.py:6661 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.60 -> 1.70 (Inc: 0.103 [Vuln:0.045, Len:0.048, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 20:09:33 | INFO     | 8760 | 22892 | app.py:2568 | extract_facts_from_message | Extracted 0 facts from message for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:33 | INFO     | 8760 | 22892 | app.py:8733 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 17.64s)
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:9429 | handle_submit_and_respond | User 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f submitted: 'i study in bit mesra...'
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:3783 | extract_and_store_knowledge | Extracted 1 general facts, 0 personal details
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:8788 | handle_user_message | Extracted 1 general facts from user message
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:3163 | analyze_and_track_interests | Detected 1 interests for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:8796 | handle_user_message | Detected interests in 1 categories
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:8320 | generate_personalized_response | --- New Request Start --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, Message: 'i study in bit mesra...'
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 61) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: user
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1413 | fetch_feedback_summary | Feedback summary fetched for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: {}
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:1708 | record_memory_access | Recorded access attempt for 12 memory entries (requested: 12).
2025-08-20 20:09:38 | INFO     | 8760 | 22892 | app.py:5146 | recall_related_memories | Recalled 12 relevant memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f' (Human-like memory retrieval with 13 candidates)
2025-08-20 20:09:39 | INFO     | 8760 | 22892 | app.py:7558 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-08-20 20:09:50 | INFO     | 8760 | 22892 | app.py:8471 | generate_personalized_response | Final Humanized Response: 'Oh, BIT Mesra! That's fantastic, Dhananjay! Production Engineering, right? I...where was I? remember...'
2025-08-20 20:09:50 | INFO     | 8760 | 22892 | app.py:1325 | log_chat_message | Chat message logged (ID: 62) for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f, role: assistant
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:8295 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.16, "memory_type": "short_term", "text_length": 20, "emotion": "caring"}
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 33, "memory_type": "short_term"}
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:4669 | _store_single_memory | Stored memory successfully (DB ID: 33, Store Idx: 32, FAISS Idx: 32, Type: short_term) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:527 | log_event | EVENT: {"event_type": "memory_save", "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "importance": 0.51, "memory_type": "micro_detail", "text_length": 29, "emotion": "caring"}
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:532 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "2f1c37bc-00ba-4e2e-ad38-5db6f523a45f", "memory_id": 34, "memory_type": "micro_detail"}
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:4669 | _store_single_memory | Stored memory successfully (DB ID: 34, Store Idx: 33, FAISS Idx: 33, Type: micro_detail) for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:4569 | store_interaction | Stored main memory + 0 detail memories for user '2f1c37bc-00ba-4e2e-ad38-5db6f523a45f'
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:1271 | load_user_profile | User profile loaded for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:1229 | save_user_profile | User profile updated for 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f. Fields updated: relationship_depth, last_active
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:6661 | update_relationship_depth | Updated relationship depth for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 1.70 -> 1.77 (Inc: 0.067 [Vuln:0.022, Len:0.035, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-08-20 20:09:52 | ERROR    | 8760 | 22892 | app.py:2428 | extract_facts_from_message | Error calling LLM for fact extraction: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 8
}
]
2025-08-20 20:09:52 | INFO     | 8760 | 22892 | app.py:2431 | extract_facts_from_message | Trying fallback model for fact extraction
2025-08-20 20:09:53 | ERROR    | 8760 | 22892 | app.py:2439 | extract_facts_from_message | Error calling fallback LLM for fact extraction: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 7
}
]
2025-08-20 20:09:53 | INFO     | 8760 | 22892 | app.py:8733 | generate_personalized_response | --- Request End --- User: 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f (Took 15.46s)
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:607 | setup_logging | Structured logging configured for production environment
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:621 | setup_gemini_api | Google Generative AI configured successfully.
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:9783 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:931 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:975 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:1046 | setup_database | Setting up/Verifying database schema (V4)...
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:1229 | setup_database | Database schema setup/verification complete (V4).
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:1827 | __init__ | AuthenticationManager initialized.
2025-08-21 13:13:08 | INFO     | 16212 | 3136 | APP.PY:6291 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-08-21 13:13:10 | INFO     | 16212 | 3136 | APP.PY:6308 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-08-21 13:13:12 | INFO     | 16212 | 3136 | APP.PY:6318 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-08-21 13:13:12 | INFO     | 16212 | 3136 | APP.PY:660 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-08-21 13:13:12 | INFO     | 16212 | 3136 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-21 13:13:16 | INFO     | 16212 | 3136 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:684 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:4468 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:1991 | _init_database_schema | Knowledge graph database schema initialized
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:1924 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:6476 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:6714 | __init__ | RelationshipManager initialized.
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:6790 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:6840 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:6882 | __init__ | NewsFetcher initialized.
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:7067 | __init__ | FestivalTracker initialized (using static list).
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:3795 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:3836 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:3224 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:7151 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:7164 | __init__ | ChatManager initialized successfully (V4).
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9796 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9797 | <module> | Operating system: win32
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9801 | <module> | CUDA not available, using CPU
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9388 | create_gradio_interface | Creating Gradio interface (V4)...
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9632 | create_gradio_interface | Gradio interface created.
2025-08-21 13:13:17 | INFO     | 16212 | 3136 | APP.PY:9824 | <module> | Launching Gradio interface...
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:607 | setup_logging | Structured logging configured for production environment
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:621 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:9783 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:931 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:975 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:1046 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:1229 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:1827 | __init__ | AuthenticationManager initialized.
2025-09-10 01:03:37 | INFO     | 29664 | 14812 | app.py:6291 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 01:03:40 | INFO     | 29664 | 14812 | app.py:6308 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 01:03:43 | INFO     | 29664 | 14812 | app.py:6318 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 01:03:43 | INFO     | 29664 | 14812 | app.py:660 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 01:03:43 | INFO     | 29664 | 14812 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:684 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:4468 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:1991 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:1924 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:6476 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:6714 | __init__ | RelationshipManager initialized.
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:6790 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:6840 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:6882 | __init__ | NewsFetcher initialized.
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:7067 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 01:03:49 | INFO     | 29664 | 14812 | app.py:3795 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:3836 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:3224 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:7151 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:7164 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9796 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9797 | <module> | Operating system: win32
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9801 | <module> | CUDA not available, using CPU
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9388 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9632 | create_gradio_interface | Gradio interface created.
2025-09-10 01:03:50 | INFO     | 29664 | 14812 | app.py:9824 | <module> | Launching Gradio interface...
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:607 | setup_logging | Structured logging configured for production environment
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:621 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:9783 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:931 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:975 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:1046 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:1229 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:1827 | __init__ | AuthenticationManager initialized.
2025-09-10 01:06:46 | INFO     | 8288 | 19340 | APP.PY:6291 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 01:06:49 | INFO     | 8288 | 19340 | APP.PY:6308 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 01:06:51 | INFO     | 8288 | 19340 | APP.PY:6318 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 01:06:51 | INFO     | 8288 | 19340 | APP.PY:660 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 01:06:51 | INFO     | 8288 | 19340 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 01:06:55 | INFO     | 8288 | 19340 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:684 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:4468 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:1991 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:1924 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:6476 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:6714 | __init__ | RelationshipManager initialized.
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:6790 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:6840 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:6882 | __init__ | NewsFetcher initialized.
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:7067 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:3795 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:3836 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:3224 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:7151 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:7164 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9796 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9797 | <module> | Operating system: win32
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9801 | <module> | CUDA not available, using CPU
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9388 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9632 | create_gradio_interface | Gradio interface created.
2025-09-10 01:06:56 | INFO     | 8288 | 19340 | APP.PY:9824 | <module> | Launching Gradio interface...
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:607 | setup_logging | Structured logging configured for production environment
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:621 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:9783 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:931 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:975 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:1046 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:1229 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:1827 | __init__ | AuthenticationManager initialized.
2025-09-10 01:09:29 | INFO     | 29652 | 25068 | APP.PY:6291 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 01:09:33 | INFO     | 29652 | 25068 | APP.PY:6308 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 01:09:40 | INFO     | 29652 | 25068 | APP.PY:6318 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 01:09:40 | INFO     | 29652 | 25068 | APP.PY:660 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 01:09:40 | INFO     | 29652 | 25068 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:684 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:4468 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:1991 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:1924 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:6476 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:6714 | __init__ | RelationshipManager initialized.
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:6790 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:6840 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:6882 | __init__ | NewsFetcher initialized.
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:7067 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:3795 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:3836 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:3224 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:7151 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:7164 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9796 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9797 | <module> | Operating system: win32
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9801 | <module> | CUDA not available, using CPU
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9388 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9632 | create_gradio_interface | Gradio interface created.
2025-09-10 01:09:44 | INFO     | 29652 | 25068 | APP.PY:9824 | <module> | Launching Gradio interface...
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:609 | setup_logging | Structured logging configured for production environment
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:10410 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 02:53:45 | INFO     | 25856 | 17016 | APP.PY:6853 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 02:53:47 | INFO     | 25856 | 17016 | APP.PY:6870 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 02:53:50 | INFO     | 25856 | 17016 | APP.PY:6880 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 02:53:50 | INFO     | 25856 | 17016 | APP.PY:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 02:53:50 | INFO     | 25856 | 17016 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 02:53:53 | INFO     | 25856 | 17016 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:5030 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7038 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7276 | __init__ | RelationshipManager initialized.
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7352 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7402 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7444 | __init__ | NewsFetcher initialized.
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7629 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:4357 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:4398 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:3227 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:3878 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7716 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:7729 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10457 | start_youtube_content_scheduler | YouTube content scheduler started successfully
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10468 | start_youtube_content_scheduler | No existing content found, running initial content fetch...
2025-09-10 02:53:54 | INFO     | 25856 | 21368 | APP.PY:10431 | daily_content_task | Starting daily YouTube content fetch...
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10485 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 02:53:54 | INFO     | 25856 | 21368 | APP.PY:4251 | fetch_and_process_daily_content | Starting daily content fetch for categories: ['sports', 'entertainment', 'travel', 'food', 'yoga', 'health']
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10486 | <module> | Operating system: win32
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10490 | <module> | CUDA not available, using CPU
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10015 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10259 | create_gradio_interface | Gradio interface created.
2025-09-10 02:53:54 | INFO     | 25856 | 17016 | APP.PY:10513 | <module> | Launching Gradio interface...
2025-09-10 02:53:55 | WARNING  | 25856 | 21368 | APP.PY:3945 | _fetch_channel_videos_rss | No videos found in RSS feed for channel UCN0OLBrPaGWWOy_gn8OlNnw
2025-09-10 02:53:58 | WARNING  | 25856 | 21368 | APP.PY:3945 | _fetch_channel_videos_rss | No videos found in RSS feed for channel UCEgdi0XIXXZ-qJOFPf4JSKw
2025-09-10 02:53:58 | INFO     | 25856 | 21368 | APP.PY:3928 | fetch_latest_videos_for_category | Fetched 4 videos for category 'sports'
2025-09-10 02:54:00 | CRITICAL | 25856 | 17016 | APP.PY:10541 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10529, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:609 | setup_logging | Structured logging configured for production environment
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:10410 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 02:54:36 | INFO     | 2576 | 21176 | APP.PY:6853 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 02:54:38 | INFO     | 2576 | 21176 | APP.PY:6870 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 02:54:40 | INFO     | 2576 | 21176 | APP.PY:6880 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 02:54:40 | INFO     | 2576 | 21176 | APP.PY:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 02:54:40 | INFO     | 2576 | 21176 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:5030 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7038 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7276 | __init__ | RelationshipManager initialized.
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7352 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7402 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7444 | __init__ | NewsFetcher initialized.
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7629 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:4357 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:4398 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:3227 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:3878 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7716 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:7729 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10457 | start_youtube_content_scheduler | YouTube content scheduler started successfully
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10468 | start_youtube_content_scheduler | No existing content found, running initial content fetch...
2025-09-10 02:54:44 | INFO     | 2576 | 13328 | APP.PY:10431 | daily_content_task | Starting daily YouTube content fetch...
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10485 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 02:54:44 | INFO     | 2576 | 13328 | APP.PY:4251 | fetch_and_process_daily_content | Starting daily content fetch for categories: ['sports', 'entertainment', 'travel', 'food', 'yoga', 'health']
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10486 | <module> | Operating system: win32
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10490 | <module> | CUDA not available, using CPU
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10015 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10259 | create_gradio_interface | Gradio interface created.
2025-09-10 02:54:44 | INFO     | 2576 | 21176 | APP.PY:10513 | <module> | Launching Gradio interface...
2025-09-10 02:54:45 | WARNING  | 2576 | 13328 | APP.PY:3945 | _fetch_channel_videos_rss | No videos found in RSS feed for channel UCN0OLBrPaGWWOy_gn8OlNnw
2025-09-10 02:54:47 | WARNING  | 2576 | 13328 | APP.PY:3945 | _fetch_channel_videos_rss | No videos found in RSS feed for channel UCEgdi0XIXXZ-qJOFPf4JSKw
2025-09-10 02:54:47 | INFO     | 2576 | 13328 | APP.PY:3928 | fetch_latest_videos_for_category | Fetched 4 videos for category 'sports'
2025-09-10 02:54:48 | WARNING  | 2576 | 13328 | APP.PY:4027 | get_video_transcript | Could not get transcript for video jU9jzAdRhNg: 
Could not retrieve a transcript for the video https://www.youtube.com/watch?v=jU9jzAdRhNg! This is most likely caused by:

YouTube is blocking requests from your IP. This usually is due to one of the following reasons:
- You have done too many requests and your IP has been blocked by YouTube
- You are doing requests from an IP belonging to a cloud provider (like AWS, Google Cloud Platform, Azure, etc.). Unfortunately, most IPs from cloud providers are blocked by YouTube.

There are two things you can do to work around this:
1. Use proxies to hide your IP address, as explained in the "Working around IP bans" section of the README (https://github.com/jdepoix/youtube-transcript-api?tab=readme-ov-file#working-around-ip-bans-requestblocked-or-ipblocked-exception).
2. (NOT RECOMMENDED) If you authenticate your requests using cookies, you will be able to continue doing requests for a while. However, YouTube will eventually permanently ban the account that you have used to authenticate with! So only do this if you don't mind your account being banned!

If you are sure that the described cause is not responsible for this error and that a transcript should be retrievable, please create an issue at https://github.com/jdepoix/youtube-transcript-api/issues. Please add which version of youtube_transcript_api you are using and provide the information needed to replicate the error. Also make sure that there are no open issues which already describe your problem!
2025-09-10 02:54:50 | CRITICAL | 2576 | 21176 | APP.PY:10541 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10529, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:10610 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 03:00:52 | INFO     | 23788 | 21756 | APP.PY:7016 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 03:00:54 | INFO     | 23788 | 21756 | APP.PY:7033 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 03:00:58 | ERROR    | 23788 | 21756 | APP.PY:7045 | __init__ | Failed to load sentiment model (cardiffnlp/twitter-roberta-base-sentiment-latest): [enforce fail at alloc_cpu.cpp:121] data. DefaultCPUAllocator: not enough memory: you tried to allocate 9437184 bytes.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 7040, in __init__
    self.sentiment_model = AutoModelForSequenceClassification.from_pretrained(sentiment_model_name)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\auto\auto_factory.py", line 564, in from_pretrained
    return model_class.from_pretrained(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\modeling_utils.py", line 3832, in from_pretrained
    model = cls(config, *model_args, **model_kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 1160, in __init__
    self.roberta = RobertaModel(config, add_pooling_layer=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 701, in __init__
    self.encoder = RobertaEncoder(config)
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 474, in __init__
    self.layer = nn.ModuleList([RobertaLayer(config) for _ in range(config.num_hidden_layers)])
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 474, in <listcomp>
    self.layer = nn.ModuleList([RobertaLayer(config) for _ in range(config.num_hidden_layers)])
                                ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 395, in __init__
    self.intermediate = RobertaIntermediate(config)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 355, in __init__
    self.dense = nn.Linear(config.hidden_size, config.intermediate_size)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\torch\nn\modules\linear.py", line 106, in __init__
    torch.empty((out_features, in_features), **factory_kwargs)
RuntimeError: [enforce fail at alloc_cpu.cpp:121] data. DefaultCPUAllocator: not enough memory: you tried to allocate 9437184 bytes.
2025-09-10 03:00:58 | WARNING  | 23788 | 21756 | APP.PY:7053 | __init__ | Sentiment analysis pipeline failed to load. Sentiment detection will be limited.
2025-09-10 03:00:58 | INFO     | 23788 | 21756 | APP.PY:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 03:00:58 | INFO     | 23788 | 21756 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:5193 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7201 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7439 | __init__ | RelationshipManager initialized.
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7515 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7565 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7607 | __init__ | NewsFetcher initialized.
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7792 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:4520 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:4561 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:3227 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:3878 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7879 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:7892 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10693 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10704 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10705 | <module> | Operating system: win32
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10709 | <module> | CUDA not available, using CPU
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10215 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10459 | create_gradio_interface | Gradio interface created.
2025-09-10 03:01:03 | INFO     | 23788 | 21756 | APP.PY:10732 | <module> | Launching Gradio interface...
2025-09-10 03:01:09 | CRITICAL | 23788 | 21756 | APP.PY:10760 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10748, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:10658 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 03:12:32 | INFO     | 9644 | 20856 | APP.PY:7062 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 03:12:34 | INFO     | 9644 | 20856 | APP.PY:7079 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 03:12:37 | INFO     | 9644 | 20856 | APP.PY:7089 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 03:12:37 | INFO     | 9644 | 20856 | APP.PY:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 03:12:37 | INFO     | 9644 | 20856 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:5239 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7247 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7485 | __init__ | RelationshipManager initialized.
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7561 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7611 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7653 | __init__ | NewsFetcher initialized.
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7838 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:4566 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:4607 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:3891 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7925 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:7938 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:10741 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:10752 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:10753 | <module> | Operating system: win32
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:10757 | <module> | CUDA not available, using CPU
2025-09-10 03:12:40 | INFO     | 9644 | 20856 | APP.PY:10263 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 03:12:41 | INFO     | 9644 | 20856 | APP.PY:10507 | create_gradio_interface | Gradio interface created.
2025-09-10 03:12:41 | INFO     | 9644 | 20856 | APP.PY:10780 | <module> | Launching Gradio interface...
2025-09-10 03:12:46 | CRITICAL | 9644 | 20856 | APP.PY:10808 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10796, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:933 | _initialize_connection_pool | Initializing database connection pool for test_youtube.db (size=8)
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'conversation_pattern' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'avg_session_gap_hours' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'preferred_chat_times' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'last_session_duration_minutes' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'total_sessions' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'longest_gap_days' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'time_zone' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1243 | _add_column_if_not_exists | Added column 'session_start_time' to table 'user_profile_v4'.
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:14:25 | INFO     | 22992 | 16696 | APP.py:3891 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:14:26 | INFO     | 22992 | 16696 | APP.py:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:14:26 | INFO     | 22992 | 16696 | APP.py:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:14:26 | INFO     | 22992 | 16696 | APP.py:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:10884 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 03:41:37 | INFO     | 14744 | 21960 | app.py:7209 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 03:41:39 | INFO     | 14744 | 21960 | app.py:7226 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 03:41:43 | INFO     | 14744 | 21960 | app.py:7236 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 03:41:43 | INFO     | 14744 | 21960 | app.py:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 03:41:43 | INFO     | 14744 | 21960 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:5386 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7394 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7632 | __init__ | RelationshipManager initialized.
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7708 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7758 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7800 | __init__ | NewsFetcher initialized.
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:7985 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:4713 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:4754 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:3910 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:8072 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:8085 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10967 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10978 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10979 | <module> | Operating system: win32
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10983 | <module> | CUDA not available, using CPU
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10489 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:10733 | create_gradio_interface | Gradio interface created.
2025-09-10 03:41:47 | INFO     | 14744 | 21960 | app.py:11006 | <module> | Launching Gradio interface...
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:10884 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 03:42:12 | INFO     | 2388 | 5504 | app.py:7209 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 03:42:14 | INFO     | 2388 | 5504 | app.py:7226 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 03:42:17 | INFO     | 2388 | 5504 | app.py:7236 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 03:42:17 | INFO     | 2388 | 5504 | app.py:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 03:42:17 | INFO     | 2388 | 5504 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:5386 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7394 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7632 | __init__ | RelationshipManager initialized.
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7708 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7758 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7800 | __init__ | NewsFetcher initialized.
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:7985 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:4713 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:4754 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:3910 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:8072 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:8085 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10967 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10978 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10979 | <module> | Operating system: win32
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10983 | <module> | CUDA not available, using CPU
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10489 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:10733 | create_gradio_interface | Gradio interface created.
2025-09-10 03:42:20 | INFO     | 2388 | 5504 | app.py:11006 | <module> | Launching Gradio interface...
2025-09-10 03:42:26 | CRITICAL | 2388 | 5504 | app.py:11034 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\app.py", line 11022, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:609 | setup_logging | Structured logging configured for production environment
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:10884 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 03:43:06 | INFO     | 18932 | 9468 | app.py:7209 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 03:43:08 | INFO     | 18932 | 9468 | app.py:7226 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 03:43:11 | INFO     | 18932 | 9468 | app.py:7236 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 03:43:11 | INFO     | 18932 | 9468 | app.py:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 03:43:11 | INFO     | 18932 | 9468 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:5386 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7394 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7632 | __init__ | RelationshipManager initialized.
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7708 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7758 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7800 | __init__ | NewsFetcher initialized.
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:7985 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:4713 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:4754 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:3910 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:8072 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:8085 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10967 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10978 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10979 | <module> | Operating system: win32
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10983 | <module> | CUDA not available, using CPU
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10489 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:10733 | create_gradio_interface | Gradio interface created.
2025-09-10 03:43:14 | INFO     | 18932 | 9468 | app.py:11006 | <module> | Launching Gradio interface...
2025-09-10 03:43:20 | CRITICAL | 18932 | 9468 | app.py:11034 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\app.py", line 11022, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:609 | setup_logging | Structured logging configured for production environment
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:623 | setup_gemini_api | Google Generative AI configured successfully.
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:10884 | <module> | --- Starting AI Friend Application V4 (Production) ---
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:933 | _initialize_connection_pool | Initializing database connection pool for ai_friend_v4.db (size=8)
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:977 | _initialize_connection_pool | Database connection pool initialized with 8 connections.
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:1048 | setup_database | Setting up/Verifying database schema (V4)...
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:1231 | setup_database | Database schema setup/verification complete (V4).
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:1829 | __init__ | AuthenticationManager initialized.
2025-09-10 20:37:23 | INFO     | 3208 | 24676 | APP.PY:7209 | __init__ | Initializing AdvancedEmotionAnalyzer: E='joeddav/distilbert-base-uncased-go-emotions-student', S='cardiffnlp/twitter-roberta-base-sentiment-latest'
2025-09-10 20:37:27 | INFO     | 3208 | 24676 | APP.PY:7226 | __init__ | Emotion model loaded: joeddav/distilbert-base-uncased-go-emotions-student on device CPU
2025-09-10 20:37:30 | INFO     | 3208 | 24676 | APP.PY:7236 | __init__ | Sentiment model loaded: cardiffnlp/twitter-roberta-base-sentiment-latest on device CPU
2025-09-10 20:37:30 | INFO     | 3208 | 24676 | APP.PY:662 | get_sentence_transformer | Loading Sentence Transformer model: all-MiniLM-L6-v2
2025-09-10 20:37:30 | INFO     | 3208 | 24676 | SentenceTransformer.py:113 | __init__ | Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-09-10 20:37:34 | INFO     | 3208 | 24676 | SentenceTransformer.py:219 | __init__ | Use pytorch device_name: cpu
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:686 | get_sentence_transformer | Sentence Transformer model 'all-MiniLM-L6-v2' loaded successfully (Dim: 384).
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:5386 | __init__ | ContextualMemoryManager initialized (V4) (Dim: 384, Index: IndexFlatIP, DB: Enabled, LoadOnInit: True)
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:1993 | _init_database_schema | Knowledge graph database schema initialized
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:1926 | __init__ | KnowledgeGraphManager initialized (V4) (Dim: 384, Index: IndexFlatIP)
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7394 | __init__ | PersonalityEngine initialized with baseline traits: {'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3}
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7632 | __init__ | RelationshipManager initialized.
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7708 | __init__ | DynamicConversationManager initialized (flow primarily driven by LLM prompt).
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7758 | __init__ | EmpathicResponseGenerator initialized (primarily provides fallback phrases).
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7800 | __init__ | NewsFetcher initialized.
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:7985 | __init__ | FestivalTracker initialized (using static list).
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:4713 | _initialize_global_knowledge_db | Global knowledge database schema initialized
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:4754 | _load_global_knowledge | Loaded 3 global knowledge entries
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:3228 | _initialize_interest_tracking_db | Interest tracking database schema initialized
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:3910 | _initialize_youtube_content_db | YouTube content database schema initialized
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:8072 | __init__ | Gemini models initialized: Main='gemini-1.5-flash', Summary='gemini-1.5-flash' with safety/generation config.
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:8085 | __init__ | ChatManager initialized successfully (V4).
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10967 | start_youtube_content_scheduler | Smart YouTube content scheduler started successfully (runs twice daily for active users)
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10978 | <module> | Python version: 3.11.9 | packaged by Anaconda, Inc. | (main, Apr 19 2024, 16:40:41) [MSC v.1916 64 bit (AMD64)]
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10979 | <module> | Operating system: win32
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10983 | <module> | CUDA not available, using CPU
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10489 | create_gradio_interface | Creating Gradio interface (V4)...
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:10733 | create_gradio_interface | Gradio interface created.
2025-09-10 20:37:35 | INFO     | 3208 | 24676 | APP.PY:11006 | <module> | Launching Gradio interface...
2025-09-10 20:37:55 | INFO     | 3208 | 23512 | APP.PY:1861 | verify_user | Attempting login for username: Dhananjay
2025-09-10 20:37:55 | WARNING  | 3208 | 23512 | APP.PY:1887 | verify_user | Login failed: Username 'Dhananjay' not found.
2025-09-10 20:38:01 | INFO     | 3208 | 23512 | APP.PY:1861 | verify_user | Attempting login for username: Dhananjay
2025-09-10 20:38:01 | WARNING  | 3208 | 23512 | APP.PY:1887 | verify_user | Login failed: Username 'Dhananjay' not found.
2025-09-10 20:38:18 | INFO     | 3208 | 23512 | APP.PY:1861 | verify_user | Attempting login for username: dhananjay
2025-09-10 20:38:18 | WARNING  | 3208 | 23512 | APP.PY:1887 | verify_user | Login failed: Username 'dhananjay' not found.
2025-09-10 20:38:21 | INFO     | 3208 | 10812 | APP.PY:1833 | register_user | Attempting registration for username: dhananjay
2025-09-10 20:38:21 | INFO     | 3208 | 10812 | APP.PY:1850 | register_user | User 'dhananjay' registered successfully with user_id: 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:22 | INFO     | 3208 | 10812 | APP.PY:1861 | verify_user | Attempting login for username: dhananjay
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:1881 | verify_user | User 'dhananjay' (ID: 96cbec64-bd37-4b75-9423-161d7b0981fb) logged in successfully.
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:5077 | start_session | Started session for user 96cbec64-bd37-4b75-9423-161d7b0981fb, gap since last: 0.0 hours
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:23 | INFO     | 3208 | 10812 | APP.PY:9977 | get_initial_greeting | Generated time-aware greeting for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 'Heya! I'm Mandy! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'hi...'
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:6686 | consolidate_memories | Consolidating memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (max_short_term: 200)
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:6704 | consolidate_memories | No need to consolidate memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'. Short-term count: 0
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'hi...'
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (0/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:38:25 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 63) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:7404 | _get_user_traits | Initialized traits for user 96cbec64-bd37-4b75-9423-161d7b0981fb from baseline.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:5427 | _get_or_create_index | Initializing FAISS index (IndexFlatIP) and memory store for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:5455 | _load_and_index_user_memories | Loading and indexing memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' from DB.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_load", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "limit": 250, "memory_type": "all"}
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_load_performance", "value": 0.0, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "count": 0, "errors": 0}
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:5468 | _load_and_index_user_memories | No memories found in DB for user '96cbec64-bd37-4b75-9423-161d7b0981fb'.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:5838 | recall_related_memories | No index or memories available for recall for user '96cbec64-bd37-4b75-9423-161d7b0981fb'.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:2035 | _get_or_create_fact_index | Initializing FAISS index for facts of user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:2062 | _load_facts_from_db | Loading and indexing facts for user '96cbec64-bd37-4b75-9423-161d7b0981fb' from DB.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:2075 | _load_facts_from_db | No facts found in DB for user '96cbec64-bd37-4b75-9423-161d7b0981fb'.
2025-09-10 20:38:26 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:7905 | get_news | Cache miss or expired for 'India trending news headlines'. Fetching fresh news.
2025-09-10 20:38:26 | INFO     | 3208 | 10812 | APP.PY:7808 | _fetch_headlines | Fetching news for query: 'India trending news headlines' (Num results: 2)
2025-09-10 20:38:47 | WARNING  | 3208 | 10812 | APP.PY:7874 | _fetch_headlines | Failed to fetch title from https://indianexpress.com/: HTTPSConnectionPool(host='indianexpress.com', port=443): Read timed out.
2025-09-10 20:38:48 | INFO     | 3208 | 23512 | APP.PY:5077 | start_session | Started session for user 96cbec64-bd37-4b75-9423-161d7b0981fb, gap since last: 0.0 hours
2025-09-10 20:38:48 | INFO     | 3208 | 23512 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:48 | INFO     | 3208 | 23512 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:48 | INFO     | 3208 | 23512 | APP.PY:9977 | get_initial_greeting | Generated time-aware greeting for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 'Hey! I'm Mandy! Back so soon? 😊' (Gap: 0.0 hrs, Pattern: new_user)
2025-09-10 20:38:48 | INFO     | 3208 | 23512 | APP.PY:10703 | handle_clear_chat | Chat cleared for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:38:55 | WARNING  | 3208 | 10812 | APP.PY:7871 | _fetch_headlines | Timeout fetching title from https://indianexpress.com/section/trending/
2025-09-10 20:39:03 | INFO     | 3208 | 10812 | APP.PY:7883 | _fetch_headlines | Processed 10 potential headlines for 'India trending news headlines'.
2025-09-10 20:39:04 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Hi there! How are you doing? It feels like ages since we last properly chatted – hope everything's g...'
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 64) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:39:09 | WARNING  | 3208 | 10812 | APP.PY:9746 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.15, "memory_type": "short_term", "text_length": 2, "emotion": "caring"}
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 35, "memory_type": "short_term"}
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 35, Store Idx: 0, FAISS Idx: 0, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:09 | WARNING  | 3208 | 10812 | APP.PY:7641 | update_relationship_depth | No interaction summary provided for user 96cbec64-bd37-4b75-9423-161d7b0981fb, applying decay only.
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.00 -> 0.01 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:39:09 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 44.60s)
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'i like cricket...'
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 1 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:9821 | handle_user_message | Detected interests in 1 categories
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'i like cricket...'
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (1/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 65) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:39:54 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:39:54 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:40:05 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, that's awesome! Cricket's such anyway, a fantastic sport – so much strategy and excitement! Are ...'
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 66) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:40:09 | WARNING  | 3208 | 10812 | APP.PY:9746 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.14, "memory_type": "short_term", "text_length": 14, "emotion": "excitement"}
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 36, "memory_type": "short_term"}
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 36, Store Idx: 1, FAISS Idx: 1, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.49, "memory_type": "micro_detail", "text_length": 24, "emotion": "excitement"}
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 37, "memory_type": "micro_detail"}
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 37, Store Idx: 2, FAISS Idx: 2, Type: micro_detail) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:40:09 | WARNING  | 3208 | 10812 | APP.PY:7641 | update_relationship_depth | No interaction summary provided for user 96cbec64-bd37-4b75-9423-161d7b0981fb, applying decay only.
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.01 -> 0.02 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:40:09 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 15.39s)
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'which is your fav cricketer...'
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 1 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:9821 | handle_user_message | Detected interests in 1 categories
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'which is your fav cricketer...'
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (2/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 67) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:42:16 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:17 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:42:17 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 2 memory entries (requested: 2).
2025-09-10 20:42:17 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 2 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 2 candidates)
2025-09-10 20:42:17 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:17 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:42:17 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:42:37 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, that's a fun question! It's tough to pick just *one* favourite, you know? Cricket's got so many ...'
2025-09-10 20:42:37 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 68) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.18, "memory_type": "short_term", "text_length": 27, "emotion": "pride"}
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 38, "memory_type": "short_term"}
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 38, Store Idx: 3, FAISS Idx: 3, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:42:40 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.02 -> 0.11 (Inc: 0.086 [Vuln:0.022, Len:0.053, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:42:42 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:42:42 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 25.20s)
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'which cricketer is your fav...'
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 1 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:9821 | handle_user_message | Detected interests in 1 categories
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'which cricketer is your fav...'
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (3/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 69) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 3 memory entries (requested: 3).
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 3 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 3 candidates)
2025-09-10 20:45:22 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:22 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:45:23 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:45:38 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, that's a tough one! Picking a *favorite* cricketer uh... feels almost impossible, you know? Ther...'
2025-09-10 20:45:44 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 70) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.18, "memory_type": "short_term", "text_length": 27, "emotion": "pride"}
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 39, "memory_type": "short_term"}
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 39, Store Idx: 4, FAISS Idx: 4, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.11 -> 0.18 (Inc: 0.076 [Vuln:0.022, Len:0.044, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:45:46 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 24.20s)
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'what about virat kohli...'
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'what about virat kohli...'
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (4/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 71) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:20 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:46:21 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 4 memory entries (requested: 4).
2025-09-10 20:46:21 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 4 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 4 candidates)
2025-09-10 20:46:21 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:21 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:46:21 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:46:36 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Ah Oh, Virat Kohli! 😊 He's a powerhouse, isn't he? Such incredible...sorry energy and focus on the f...'
2025-09-10 20:46:36 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 72) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.2, "memory_type": "short_term", "text_length": 22, "emotion": "curiosity"}
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 40, "memory_type": "short_term"}
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 40, Store Idx: 5, FAISS Idx: 5, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:46:37 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.18 -> 0.26 (Inc: 0.082 [Vuln:0.022, Len:0.050, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:46:38 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:38 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 17.34s)
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'msd...'
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'msd...'
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (5/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 73) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:46:40 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:46:40 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'MSD… Ah, hte Captain Cool himself! mean MSD… Ah, the Captain Cool himself! 😊 What a legend. Makes me...'
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 74) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:46:53 | WARNING  | 3208 | 10812 | APP.PY:9746 | generate_personalized_response | Summarization failed, storing memory with basic importance.
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.13, "memory_type": "short_term", "text_length": 3, "emotion": "caring"}
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 41, "memory_type": "short_term"}
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 41, Store Idx: 6, FAISS Idx: 6, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:46:53 | WARNING  | 3208 | 10812 | APP.PY:7641 | update_relationship_depth | No interaction summary provided for user 96cbec64-bd37-4b75-9423-161d7b0981fb, applying decay only.
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.26 -> 0.27 (Inc: 0.010 [Vuln:0.000, Len:0.000, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:46:53 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 13.34s)
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'sorry i forgot to introduce my name is dhananjay...'
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'sorry i forgot to introduce my name is dhananjay...'
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:8241 | _extract_and_update_profile_info | Extracted potential name 'Dhananjay' for user 96cbec64-bd37-4b75-9423-161d7b0981fb via regex pattern: (?:my\s+name\s+is|call\s+me|i'm|i\s+am)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: name, last_active
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:8251 | _extract_and_update_profile_info | Saved extracted name 'Dhananjay' to DB for user 96cbec64-bd37-4b75-9423-161d7b0981fb.
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (6/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:47:08 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 75) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:47:09 | WARNING  | 3208 | 10812 | APP.PY:2378 | search_facts | No facts indexed for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:47:09 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:47:23 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! That's perfectly alright No worries at all! It happens to the best of us. I'm so glad...'
2025-09-10 20:47:26 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 76) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.24, "memory_type": "short_term", "text_length": 48, "emotion": "remorse"}
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 42, "memory_type": "short_term"}
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 42, Store Idx: 7, FAISS Idx: 7, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:47:28 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.27 -> 0.35 (Inc: 0.074 [Vuln:0.022, Len:0.042, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:47:29 | INFO     | 3208 | 10812 | APP.PY:2347 | add_fact | Added fact for user 96cbec64-bd37-4b75-9423-161d7b0981fb: The user's name is Dhananjay.... with ID 9221fa1f-5939-4e9c-bde6-0533d79cc5a4
2025-09-10 20:47:29 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 1 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:29 | INFO     | 3208 | 10812 | APP.PY:9742 | generate_personalized_response | Extracted 1 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:47:29 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 20.26s)
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'which odi is comeing up...'
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 1 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:9821 | handle_user_message | Detected interests in 1 categories
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'which odi is comeing up...'
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (7/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 77) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 5 memory entries (requested: 5).
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 5 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 5 candidates)
2025-09-10 20:50:32 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:50:33 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:50:48 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, hey Dhananjay! ODI, you say? Cricket, right? Makes me think of our last chat – all that passiona...'
2025-09-10 20:50:51 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 78) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.17, "memory_type": "short_term", "text_length": 23, "emotion": "curiosity"}
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 43, "memory_type": "short_term"}
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 43, Store Idx: 8, FAISS Idx: 8, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:50:54 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.35 -> 0.42 (Inc: 0.074 [Vuln:0.022, Len:0.042, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:50:55 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:50:55 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 22.81s)
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'check you check and tell i am travelign...'
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 1 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:9821 | handle_user_message | Detected interests in 1 categories
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'check you check and tell i am travelign...'
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (8/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 79) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:51:08 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:51:09 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:51:17 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! Traveling, huh? That's exciting! Where to? Um... makes me think of that time I went t...'
2025-09-10 20:51:20 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 80) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=1
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.18, "memory_type": "short_term", "text_length": 39, "emotion": "curiosity"}
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 44, "memory_type": "short_term"}
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 44, Store Idx: 9, FAISS Idx: 9, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.53, "memory_type": "micro_detail", "text_length": 26, "emotion": "curiosity"}
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 45, "memory_type": "micro_detail"}
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 45, Store Idx: 10, FAISS Idx: 10, Type: micro_detail) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:51:22 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.42 -> 0.52 (Inc: 0.094 [Vuln:0.045, Len:0.039, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:51:23 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:23 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 14.93s)
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'act as a expert coder and write a simple code in c...'
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'act as a expert coder and write a simple code in c...'
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (9/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 81) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:34 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:51:35 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:51:35 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:51:35 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:51:51 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! Coding in C, huh? That's...intense! Reminds me a little of trying to untangle all the...'
2025-09-10 20:51:55 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 82) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.20, Reci=False, Insights=2
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.24, "memory_type": "short_term", "text_length": 50, "emotion": "caring"}
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 46, "memory_type": "short_term"}
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 46, Store Idx: 11, FAISS Idx: 11, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:51:57 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.52 -> 0.62 (Inc: 0.106 [Vuln:0.045, Len:0.051, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:51:58 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:51:58 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 23.46s)
2025-09-10 20:52:01 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'i was pulling your leg ...'
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'i was pulling your leg ...'
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (10/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 83) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 0 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:52:02 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:52:14 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! You got me! 😅 I should have known better than to get all worked up about a little C c...'
2025-09-10 20:52:14 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 84) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.16, "memory_type": "short_term", "text_length": 23, "emotion": "nervousness"}
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 47, "memory_type": "short_term"}
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 47, Store Idx: 12, FAISS Idx: 12, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.51, "memory_type": "micro_detail", "text_length": 33, "emotion": "nervousness"}
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 48, "memory_type": "micro_detail"}
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 48, Store Idx: 13, FAISS Idx: 13, Type: micro_detail) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:52:16 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.62 -> 0.70 (Inc: 0.080 [Vuln:0.022, Len:0.048, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:52:18 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:18 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 15.99s)
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'please tell your fav crickter...'
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'please tell your fav crickter...'
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (11/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 85) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 3 memory entries (requested: 3).
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 3 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 2 candidates)
2025-09-10 20:52:30 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:52:31 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:52:53 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! That's a fun question! You know, it’s funny, I was just thinking about that earlier –...'
2025-09-10 20:52:53 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 86) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.00, Reci=False, Insights=1
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.14, "memory_type": "short_term", "text_length": 29, "emotion": "desire"}
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 49, "memory_type": "short_term"}
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 49, Store Idx: 14, FAISS Idx: 14, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:52:54 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.70 -> 0.76 (Inc: 0.052 [Vuln:0.000, Len:0.042, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:52:55 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:52:55 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 24.69s)
2025-09-10 20:53:26 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'can you type less your write whole para ...'
2025-09-10 20:53:26 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:53:26 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:26 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'can you type less your write whole para ...'
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (12/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 87) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 1 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 0 candidates)
2025-09-10 20:53:27 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:53:28 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:53:39 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, Dhananjay! Sorry about the long paragraphs earlier. I get a bit carried away sometimes, you know...'
2025-09-10 20:53:39 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 88) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.18, "memory_type": "short_term", "text_length": 40, "emotion": "curiosity"}
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 50, "memory_type": "short_term"}
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 50, Store Idx: 15, FAISS Idx: 15, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:53:41 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.76 -> 0.84 (Inc: 0.080 [Vuln:0.022, Len:0.048, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:53:42 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 0 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:42 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 15.24s)
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:10635 | handle_submit_and_respond | User 96cbec64-bd37-4b75-9423-161d7b0981fb submitted: 'i am intrested in cricktet...'
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:4795 | extract_and_store_knowledge | Extracted 0 general facts, 0 personal details
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:3247 | analyze_and_track_interests | Detected 0 interests for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:9345 | generate_personalized_response | --- New Request Start --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb, Message: 'i am intrested in cricktet...'
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:8117 | _analyze_and_update_user_style | Initial user style analysis for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:8150 | _analyze_and_update_user_style | Insufficient user messages (13/15) for style analysis (User: 96cbec64-bd37-4b75-9423-161d7b0981fb). Will try later.
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 89) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: user
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1490 | fetch_feedback_summary | Feedback summary fetched for user 96cbec64-bd37-4b75-9423-161d7b0981fb: {}
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:1785 | record_memory_access | Recorded access attempt for 1 memory entries (requested: 1).
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:6168 | recall_related_memories | Recalled 1 relevant memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb' (Human-like memory retrieval with 1 candidates)
2025-09-10 20:53:53 | INFO     | 3208 | 10812 | APP.PY:7945 | get_relevant_news_summary | Fetching general trending news.
2025-09-10 20:53:54 | INFO     | 3208 | 10812 | APP.PY:8583 | _generate_llm_response_stream | Sending request to Gemini model gemini-1.5-flash...
2025-09-10 20:54:06 | INFO     | 3208 | 10812 | APP.PY:9496 | generate_personalized_response | Final Humanized Response: 'Oh, cool! Cricket, huh? That's awesome, Dhananjay. I remember you were asking about my favorite cric...'
2025-09-10 20:54:09 | INFO     | 3208 | 10812 | APP.PY:1402 | log_chat_message | Chat message logged (ID: 90) for user 96cbec64-bd37-4b75-9423-161d7b0981fb, role: assistant
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:9320 | _summarize_interaction | Interaction summarized. Vuln=0.10, Reci=False, Insights=1
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.17, "memory_type": "short_term", "text_length": 26, "emotion": "confusion"}
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 51, "memory_type": "short_term"}
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 51, Store Idx: 16, FAISS Idx: 16, Type: short_term) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:598 | log_event | EVENT: {"event_type": "memory_save", "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "importance": 0.52, "memory_type": "micro_detail", "text_length": 38, "emotion": "confusion"}
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:603 | log_metric | METRIC: {"metric": "memory_save_success", "value": 1, "user_id": "96cbec64-bd37-4b75-9423-161d7b0981fb", "memory_id": 52, "memory_type": "micro_detail"}
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:5691 | _store_single_memory | Stored memory successfully (DB ID: 52, Store Idx: 17, FAISS Idx: 17, Type: micro_detail) for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:5591 | store_interaction | Stored main memory + 0 detail memories for user '96cbec64-bd37-4b75-9423-161d7b0981fb'
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:1348 | load_user_profile | User profile loaded for 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:1306 | save_user_profile | User profile updated for 96cbec64-bd37-4b75-9423-161d7b0981fb. Fields updated: relationship_depth, last_active
2025-09-10 20:54:11 | INFO     | 3208 | 10812 | APP.PY:7683 | update_relationship_depth | Updated relationship depth for user 96cbec64-bd37-4b75-9423-161d7b0981fb: 0.84 -> 0.92 (Inc: 0.086 [Vuln:0.022, Len:0.053, Reci:0.000], Decay: 0.000 from 0.0 days inactive)
2025-09-10 20:54:12 | INFO     | 3208 | 10812 | APP.PY:2347 | add_fact | Added fact for user 96cbec64-bd37-4b75-9423-161d7b0981fb: The user is interested in cricket.... with ID 323794f7-7ebb-4dfe-afd5-7d9cf403ccd3
2025-09-10 20:54:12 | INFO     | 3208 | 10812 | APP.PY:2649 | extract_facts_from_message | Extracted 1 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:54:12 | INFO     | 3208 | 10812 | APP.PY:9742 | generate_personalized_response | Extracted 1 facts from message for user 96cbec64-bd37-4b75-9423-161d7b0981fb
2025-09-10 20:54:12 | INFO     | 3208 | 10812 | APP.PY:9758 | generate_personalized_response | --- Request End --- User: 96cbec64-bd37-4b75-9423-161d7b0981fb (Took 19.36s)
2025-09-11 01:29:32 | INFO     | 3208 | 24676 | APP.PY:11031 | <module> | Gradio application closed.
