"""
Event handlers for the AI Friend UI.
Contains all the callback functions for user interactions.
"""

import time
from typing import Any, Dict, Generator, List, Optional, Tuple

import gradio as gr

from ..auth import AuthenticationManager
from ..chat import Chat<PERSON>anager
from ..core import LoggerMixin, sanitize_text
from ..database import DatabaseManager
from .components import format_error_message, format_success_message, validate_input_length


class UIHandlers(LoggerMixin):
    """Handles UI events and user interactions."""
    
    def __init__(self, db_manager: DatabaseManager, auth_manager: AuthenticationManager, chat_manager: ChatManager):
        """
        Initialize UI handlers.
        
        Args:
            db_manager: Database manager instance
            auth_manager: Authentication manager instance
            chat_manager: Chat manager instance
        """
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.chat_manager = chat_manager
        self.logger.info("UI handlers initialized")
    
    def handle_login(self, username: str, password: str) -> Tuple[str, bool, str, str, List[List[str]]]:
        """
        Handle user login.
        
        Args:
            username: Username
            password: Password
            
        Returns:
            Tuple of (message, is_logged_in, session_token, user_name, initial_chat_history)
        """
        try:
            self.logger.info(f"Login attempt for username: {username}")
            
            # Validate inputs
            if not username or not password:
                return format_error_message("Please enter both username and password"), False, "", "", []
            
            # Sanitize inputs
            username = sanitize_text(username, 50)
            
            # Authenticate user
            success, session_token, message = self.auth_manager.authenticate_user(username, password)
            
            if success and session_token:
                user_id = self.auth_manager.get_user_id_from_token(session_token)
                if not user_id:
                    return format_error_message("Invalid session. Please log in again."), False, "", "", []

                # Get user profile
                user_profile = self.auth_manager.get_user_profile(user_id)
                user_name = user_profile.get('name', username) if user_profile else username
                
                # Generate initial greeting
                greeting = self.chat_manager.get_initial_greeting(user_id)
                initial_history = [["", greeting]]
                
                self.logger.info(f"User {username} logged in successfully")
                return format_success_message("Login successful!"), True, session_token, user_name, initial_history
            else:
                self.logger.warning(f"Login failed for username: {username}")
                return format_error_message(message), False, "", "", []
                
        except Exception as e:
            self.logger.error(f"Login error: {e}")
            return format_error_message("Login failed due to server error"), False, "", "", []
    
    def handle_register(self, username: str, name: str, password: str) -> Tuple[str, bool, str, str, List[List[str]]]:
        """
        Handle user registration.
        
        Args:
            username: Desired username
            name: Display name
            password: Password
            
        Returns:
            Tuple of (message, is_logged_in, session_token, user_name, initial_chat_history)
        """
        try:
            self.logger.info(f"Registration attempt for username: {username}")
            
            # Validate inputs
            if not username or not password:
                return format_error_message("Please enter both username and password"), False, "", "", []
            
            # Sanitize inputs
            username = sanitize_text(username, 30)
            name = sanitize_text(name, 50) if name else username
            
            # Register user
            success, message, session_token = self.auth_manager.register_user(username, password, name)
            
            if success and session_token:
                user_id = self.auth_manager.get_user_id_from_token(session_token)
                if not user_id:
                    return format_error_message("Invalid session. Please log in again."), False, "", "", []

                # Generate initial greeting
                greeting = self.chat_manager.get_initial_greeting(user_id)
                initial_history = [["", greeting]]
                
                self.logger.info(f"User {username} registered successfully")
                return format_success_message("Registration successful!"), True, session_token, name, initial_history
            else:
                self.logger.warning(f"Registration failed for username: {username}")
                return format_error_message(message), False, "", "", []
                
        except Exception as e:
            self.logger.error(f"Registration error: {e}")
            return format_error_message("Registration failed due to server error"), False, "", "", []
    
    def handle_logout(self) -> Tuple[bool, str, str, List[List[str]], str]:
        """
        Handle user logout.
        
        Returns:
            Tuple of (is_logged_in, session_token, user_name, chat_history, message_input)
        """
        try:
            self.logger.info("User logout")
            return False, "", "", [], ""
        except Exception as e:
            self.logger.error(f"Logout error: {e}")
            return False, "", "", [], ""
    
    def handle_send_message(self, message: str, chat_history: List[List[str]], 
                           session_token: str) -> Generator[Tuple[List[List[str]], str], None, None]:
        """
        Handle sending a message.
        
        Args:
            message: User message
            chat_history: Current chat history
            session_token: The user's session token.
            
        Yields:
            Tuple of (updated_chat_history, cleared_message_input)
        """
        try:
            user_id = self.auth_manager.get_user_id_from_token(session_token)
            if not user_id:
                error_history = chat_history + [[message, "❌ Your session has expired. Please log in again."]]
                yield error_history, ""
                return

            is_valid, error_msg = validate_input_length(message, 1000)
            if not is_valid:
                # Add error message to chat
                error_history = chat_history + [[message, f"❌ {error_msg}"]]
                yield error_history, ""
                return
            
            # Sanitize message
            clean_message = sanitize_text(message, 1000)
            
            # Convert Gradio chat history to internal format
            internal_history = self._convert_gradio_to_internal_history(chat_history)
            
            # Process message and get streaming response
            response_generator = self.chat_manager.handle_user_message(user_id, clean_message, internal_history)
            
            for updated_internal_history in response_generator:
                # Convert back to Gradio format
                gradio_history = self._convert_internal_to_gradio_history(updated_internal_history)
                yield gradio_history, ""
                
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
            error_history = chat_history + [[message, "❌ Sorry, I'm having trouble processing your message."]]
            yield error_history, ""
    
    def handle_feedback(self, rating: int, feedback_type: str, comment: str, 
                       chat_history: List[List[str]], session_token: str) -> str:
        """
        Handle user feedback.
        
        Args:
            rating: Feedback rating (-1 or 1)
            feedback_type: Type of feedback
            comment: Optional comment
            chat_history: Current chat history
            session_token: The user's session token.
            
        Returns:
            Feedback confirmation message
        """
        try:
            user_id = self.auth_manager.get_user_id_from_token(session_token)
            if not user_id:
                return format_error_message("Your session has expired. Please log in again to provide feedback")

            # Convert chat history to internal format
            internal_history = self._convert_gradio_to_internal_history(chat_history)
            
            # Handle feedback
            self.chat_manager.handle_feedback(user_id, rating, feedback_type, internal_history, comment)
            
            rating_text = "positive" if rating > 0 else "negative"
            return format_success_message(f"Thank you for your {rating_text} feedback!")
            
        except Exception as e:
            self.logger.error(f"Error handling feedback: {e}")
            return format_error_message("Failed to save feedback")
    
    def create_feedback_handler(self, rating: int) -> callable:
        """
        Create a feedback handler for a specific rating.
        
        Args:
            rating: Feedback rating (-1 or 1)
            
        Returns:
            Feedback handler function
        """
        def handler(feedback_type: str, comment: str, chat_history: List[List[str]], session_token: str) -> str:
            return self.handle_feedback(rating, feedback_type, comment, chat_history, session_token)
        
        return handler
    
    def handle_enter_key(self, message: str, chat_history: List[List[str]], 
                        session_token: str) -> Generator[Tuple[List[List[str]], str], None, None]:
        """
        Handle Enter key press in message input.
        
        Args:
            message: User message
            chat_history: Current chat history
            session_token: The user's session token.
            
        Yields:
            Tuple of (updated_chat_history, cleared_message_input)
        """
        # Same as send message
        yield from self.handle_send_message(message, chat_history, session_token)
    
    def _convert_gradio_to_internal_history(self, gradio_history: List[List[str]]) -> List[Dict[str, str]]:
        """
        Convert Gradio chat history format to internal format.
        
        Args:
            gradio_history: Gradio format [[user_msg, bot_msg], ...]
            
        Returns:
            Internal format [{"role": "user", "content": "..."}, ...]
        """
        internal_history = []
        
        for pair in gradio_history:
            if len(pair) >= 2:
                user_msg = pair[0]
                bot_msg = pair[1]
                
                if user_msg and user_msg.strip():
                    internal_history.append({"role": "user", "content": user_msg.strip()})
                
                if bot_msg and bot_msg.strip():
                    internal_history.append({"role": "assistant", "content": bot_msg.strip()})
        
        return internal_history
    
    def _convert_internal_to_gradio_history(self, internal_history: List[Dict[str, str]]) -> List[List[str]]:
        """
        Convert internal chat history format to Gradio format.
        
        Args:
            internal_history: Internal format [{"role": "user", "content": "..."}, ...]
            
        Returns:
            Gradio format [[user_msg, bot_msg], ...]
        """
        gradio_history = []
        current_pair = ["", ""]
        
        for msg in internal_history:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "user":
                # Start new pair
                if current_pair[0] or current_pair[1]:
                    gradio_history.append(current_pair)
                current_pair = [content, ""]
            
            elif role == "assistant":
                # Complete current pair
                current_pair[1] = content
        
        # Add final pair if it has content
        if current_pair[0] or current_pair[1]:
            gradio_history.append(current_pair)
        
        return gradio_history
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get system health status.
        
        Returns:
            Health status dictionary
        """
        try:
            return {
                "database": self.db_manager.health_check(),
                "timestamp": time.time()
            }
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            return {
                "database": False,
                "timestamp": time.time(),
                "error": str(e)
            }