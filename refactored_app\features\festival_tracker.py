"""
Festival Tracker for cultural awareness and celebration.
Tracks Indian festivals and cultural events for contextual awareness.
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List

try:
    from ..config import config
    from ..core import LoggerMixin
except Exception:
    from config import config
    from core import LoggerMixin


class FestivalTracker(LoggerMixin):
    """
    Tracks Indian festivals and cultural events for contextual awareness.
    Provides festival information and celebration context.
    """
    
    def __init__(self):
        """Initialize festival tracker with Indian festivals."""
        # Festival data - dates should be updated annually
        self.festivals = {
            # 2024 Examples (Update these annually!)
            "2024-01-14": {"name": "Makar Sankranti", "type": "Hindu", "region": "Pan-India"},
            "2024-01-15": {"name": "Pongal", "type": "Hindu", "region": "Tamil Nadu"},
            "2024-01-26": {"name": "Republic Day", "type": "National", "region": "India"},
            "2024-03-08": {"name": "Holi", "type": "Hindu", "region": "North India"},
            "2024-03-25": {"name": "<PERSON>udi Padwa", "type": "Hindu", "region": "Maharashtra"},
            "2024-04-09": {"name": "Ram Navami", "type": "Hindu", "region": "Pan-India"},
            "2024-04-14": {"name": "Baisakhi", "type": "Sikh", "region": "Punjab"},
            "2024-04-21": {"name": "Easter", "type": "Christian", "region": "Pan-India"},
            "2024-08-15": {"name": "Independence Day", "type": "National", "region": "India"},
            "2024-08-19": {"name": "Janmashtami", "type": "Hindu", "region": "Pan-India"},
            "2024-09-07": {"name": "Ganesh Chaturthi", "type": "Hindu", "region": "Maharashtra"},
            "2024-10-02": {"name": "Gandhi Jayanti", "type": "National", "region": "India"},
            "2024-10-12": {"name": "Dussehra", "type": "Hindu", "region": "Pan-India"},
            "2024-11-01": {"name": "Diwali", "type": "Hindu", "region": "Pan-India"},
            "2024-11-15": {"name": "Guru Nanak Jayanti", "type": "Sikh", "region": "Punjab"},
            "2024-12-25": {"name": "Christmas", "type": "Christian", "region": "Pan-India"},
            
            # 2025 Examples (Add more as needed)
            "2025-01-14": {"name": "Makar Sankranti", "type": "Hindu", "region": "Pan-India"},
            "2025-01-26": {"name": "Republic Day", "type": "National", "region": "India"},
            "2025-03-14": {"name": "Holi", "type": "Hindu", "region": "North India"},
            "2025-08-15": {"name": "Independence Day", "type": "National", "region": "India"},
            "2025-10-02": {"name": "Gandhi Jayanti", "type": "National", "region": "India"},
            "2025-12-25": {"name": "Christmas", "type": "Christian", "region": "Pan-India"},
        }
        
        # Festival greetings and messages
        self.festival_greetings = {
            "Diwali": [
                "Happy Diwali! 🪔✨ Hope your home is filled with light and joy!",
                "Wishing you a very Happy Diwali! May this festival bring prosperity and happiness! 🪔",
                "Happy Diwali! Hope you're having a wonderful celebration with family! ✨"
            ],
            "Holi": [
                "Happy Holi! 🌈 Hope you're having a colorful and joyful celebration!",
                "Wishing you a vibrant and happy Holi! 🎨 May your life be filled with colors of joy!",
                "Happy Holi! Hope you're enjoying the festival of colors! 🌈"
            ],
            "Dussehra": [
                "Happy Dussehra! 🏹 May good always triumph over evil in your life!",
                "Wishing you a blessed Dussehra! May this festival bring victory and prosperity!",
                "Happy Dussehra! Hope you're celebrating the victory of good over evil! 🏹"
            ],
            "Independence Day": [
                "Happy Independence Day! 🇮🇳 Proud to be Indian! Jai Hind!",
                "Wishing you a very Happy Independence Day! 🇮🇳 Let's celebrate our freedom!",
                "Happy 15th August! 🇮🇳 Hope you're celebrating our beautiful nation!"
            ],
            "Republic Day": [
                "Happy Republic Day! 🇮🇳 Proud of our constitution and democracy!",
                "Wishing you a wonderful Republic Day! 🇮🇳 Jai Hind!",
                "Happy 26th January! 🇮🇳 Celebrating our republic and its values!"
            ],
            "Christmas": [
                "Merry Christmas! 🎄✨ Hope you're having a wonderful celebration!",
                "Wishing you a very Merry Christmas! 🎄 May your day be filled with joy and love!",
                "Merry Christmas! 🎄 Hope Santa brought you everything you wished for!"
            ],
            "Ganesh Chaturthi": [
                "Ganpati Bappa Morya! 🐘 Happy Ganesh Chaturthi! May Ganesha bless you!",
                "Wishing you a blessed Ganesh Chaturthi! 🐘 May all obstacles be removed from your path!",
                "Happy Ganesh Chaturthi! 🐘 Ganpati Bappa Morya!"
            ]
        }
        
        # Cache for today's festival to avoid repeated lookups
        self.today_cache = None
        self.cache_date = None
        
        self.logger.info("Festival tracker initialized with Indian festivals")
    
    def get_today_festival(self) -> Optional[Dict[str, Any]]:
        """Get festival information for today."""
        try:
            today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
            
            # Check cache first
            if self.cache_date == today and self.today_cache is not None:
                return self.today_cache
            
            # Look up today's festival
            found_festival = None
            if today in self.festivals:
                festival_info = self.festivals[today].copy()
                festival_info['date'] = today
                festival_info['is_today'] = True
                found_festival = festival_info
            
            # Update cache
            self.cache_date = today
            self.today_cache = found_festival
            
            return found_festival
            
        except Exception as e:
            self.logger.error(f"Error getting today's festival: {e}")
            return None
    
    def get_upcoming_festivals(self, days_ahead: int = 7) -> List[Dict[str, Any]]:
        """Get festivals coming up in the next few days."""
        try:
            today = datetime.now(timezone.utc)
            upcoming = []
            
            for i in range(1, days_ahead + 1):
                future_date = today + timedelta(days=i)
                date_str = future_date.strftime("%Y-%m-%d")
                
                if date_str in self.festivals:
                    festival_info = self.festivals[date_str].copy()
                    festival_info['date'] = date_str
                    festival_info['days_away'] = i
                    upcoming.append(festival_info)
            
            return upcoming
            
        except Exception as e:
            self.logger.error(f"Error getting upcoming festivals: {e}")
            return []
    
    def get_festival_greeting(self, festival_name: str) -> Optional[str]:
        """Get a random greeting for a specific festival."""
        try:
            import random
            
            greetings = self.festival_greetings.get(festival_name, [])
            if greetings:
                return random.choice(greetings)
            
            # Generic greeting for festivals not in the list
            return f"Happy {festival_name}! Hope you're having a wonderful celebration! 🎉"
            
        except Exception as e:
            self.logger.error(f"Error getting festival greeting: {e}")
            return None
    
    def get_festival_context(self) -> Dict[str, Any]:
        """Get comprehensive festival context for conversation."""
        try:
            today_festival = self.get_today_festival()
            upcoming_festivals = self.get_upcoming_festivals(7)
            
            context = {
                'today_festival': today_festival,
                'upcoming_festivals': upcoming_festivals,
                'has_festival_today': today_festival is not None,
                'has_upcoming_festivals': len(upcoming_festivals) > 0
            }
            
            # Add greeting if there's a festival today
            if today_festival:
                greeting = self.get_festival_greeting(today_festival['name'])
                context['festival_greeting'] = greeting
            
            # Add upcoming festival info
            if upcoming_festivals:
                next_festival = upcoming_festivals[0]
                context['next_festival'] = next_festival
                context['next_festival_days'] = next_festival['days_away']
            
            return context
            
        except Exception as e:
            self.logger.error(f"Error getting festival context: {e}")
            return {
                'today_festival': None,
                'upcoming_festivals': [],
                'has_festival_today': False,
                'has_upcoming_festivals': False
            }
    
    def is_festival_season(self) -> bool:
        """Check if we're in a major festival season."""
        try:
            today = datetime.now(timezone.utc)
            month = today.month
            
            # Major festival seasons in India
            festival_seasons = [
                (8, 11),   # August to November (Janmashtami to Diwali)
                (3, 4),    # March to April (Holi to Ram Navami)
                (12, 1),   # December to January (Christmas to Makar Sankranti)
            ]
            
            for start_month, end_month in festival_seasons:
                if start_month <= end_month:
                    if start_month <= month <= end_month:
                        return True
                else:  # Wraps around year (Dec-Jan)
                    if month >= start_month or month <= end_month:
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking festival season: {e}")
            return False
    
    def get_festival_by_name(self, festival_name: str) -> Optional[Dict[str, Any]]:
        """Find festival information by name."""
        try:
            for date, festival_info in self.festivals.items():
                if festival_info['name'].lower() == festival_name.lower():
                    result = festival_info.copy()
                    result['date'] = date
                    return result
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding festival by name: {e}")
            return None
    
    def get_festivals_by_type(self, festival_type: str) -> List[Dict[str, Any]]:
        """Get all festivals of a specific type (Hindu, Christian, National, etc.)."""
        try:
            festivals = []
            
            for date, festival_info in self.festivals.items():
                if festival_info['type'].lower() == festival_type.lower():
                    result = festival_info.copy()
                    result['date'] = date
                    festivals.append(result)
            
            # Sort by date
            festivals.sort(key=lambda x: x['date'])
            return festivals
            
        except Exception as e:
            self.logger.error(f"Error getting festivals by type: {e}")
            return []
    
    def get_festivals_by_region(self, region: str) -> List[Dict[str, Any]]:
        """Get festivals celebrated in a specific region."""
        try:
            festivals = []
            
            for date, festival_info in self.festivals.items():
                festival_region = festival_info['region'].lower()
                if (region.lower() in festival_region or 
                    festival_region == 'pan-india' or 
                    festival_region == 'india'):
                    result = festival_info.copy()
                    result['date'] = date
                    festivals.append(result)
            
            # Sort by date
            festivals.sort(key=lambda x: x['date'])
            return festivals
            
        except Exception as e:
            self.logger.error(f"Error getting festivals by region: {e}")
            return []
    
    def add_festival(self, date: str, name: str, festival_type: str, region: str) -> bool:
        """Add a new festival to the tracker."""
        try:
            self.festivals[date] = {
                'name': name,
                'type': festival_type,
                'region': region
            }
            
            self.logger.info(f"Added festival: {name} on {date}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding festival: {e}")
            return False
    
    def get_festival_summary(self) -> Dict[str, Any]:
        """Get summary statistics about tracked festivals."""
        try:
            total_festivals = len(self.festivals)
            
            # Count by type
            type_counts = {}
            region_counts = {}
            
            for festival_info in self.festivals.values():
                festival_type = festival_info['type']
                region = festival_info['region']
                
                type_counts[festival_type] = type_counts.get(festival_type, 0) + 1
                region_counts[region] = region_counts.get(region, 0) + 1
            
            return {
                'total_festivals': total_festivals,
                'festivals_by_type': type_counts,
                'festivals_by_region': region_counts,
                'festival_season': self.is_festival_season()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting festival summary: {e}")
            return {}
