"""
Database models and schema definitions for AI Friend application.
Defines the structure of all database tables and relationships.
"""

from dataclasses import dataclass
from typing import Any, Dict, List, Optional

try:
    from ..core import get_current_timestamp
except ImportError:
    from core import get_current_timestamp


@dataclass
class UserProfile:
    """User profile model."""
    user_id: str
    username: str
    password_hash: str
    name: Optional[str] = None
    interests: List[str] = None
    preferred_style: Optional[str] = None
    recurring_topics: List[str] = None
    emotional_patterns: Dict[str, float] = None
    relationship_depth: float = 0.0
    last_style_analysis_ts: Optional[str] = None
    avg_msg_length: float = 50.0
    emoji_frequency: float = 0.1
    question_rate: float = 0.2
    formality_score: float = 0.5
    conversation_pattern: str = "new_user"
    avg_session_gap_hours: float = 24.0
    preferred_chat_times: str = ""
    last_session_duration_minutes: float = 0.0
    total_sessions: int = 0
    longest_gap_days: float = 0.0
    time_zone: str = "UTC"
    session_start_time: str = ""
    last_active: Optional[str] = None
    created_at: Optional[str] = None
    
    def __post_init__(self):
        if self.interests is None:
            self.interests = []
        if self.recurring_topics is None:
            self.recurring_topics = []
        if self.emotional_patterns is None:
            self.emotional_patterns = {}
        if self.created_at is None:
            self.created_at = get_current_timestamp()
        if self.last_active is None:
            self.last_active = get_current_timestamp()


@dataclass
class ChatMessage:
    """Chat message model."""
    id: Optional[int] = None
    user_id: str = ""
    timestamp: str = ""
    role: str = ""  # 'user', 'assistant', 'system'
    content: str = ""
    emotion_analysis: Optional[Dict[str, Any]] = None
    sentiment: Optional[str] = None
    prompted_by_user_log_id: Optional[int] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = get_current_timestamp()


@dataclass
class Feedback:
    """Feedback model."""
    id: Optional[int] = None
    user_id: str = ""
    chat_log_id: Optional[int] = None
    message_content: Optional[str] = None
    rating: int = 0  # -1 or 1
    feedback_type: str = ""  # 'general', 'humor', 'empathy', 'memory'
    comment: Optional[str] = None
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = get_current_timestamp()


@dataclass
class Memory:
    """Memory model."""
    id: Optional[int] = None
    user_id: str = ""
    text: str = ""
    embedding: Optional[bytes] = None
    emotion: str = "neutral"
    importance: float = 0.5
    timestamp: str = ""
    last_accessed: Optional[str] = None
    access_count: int = 0
    related_chat_log_id: Optional[int] = None
    memory_type: str = "short_term"  # 'short_term' or 'long_term'
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = get_current_timestamp()
        if self.last_accessed is None:
            self.last_accessed = get_current_timestamp()


@dataclass
class ConversationSession:
    """Conversation session model."""
    id: Optional[int] = None
    user_id: str = ""
    session_start: str = ""
    session_end: Optional[str] = None
    message_count: int = 0
    session_duration_minutes: float = 0.0
    gap_since_last_hours: float = 0.0
    day_of_week: Optional[int] = None
    hour_of_day: Optional[int] = None
    created_at: str = ""
    
    def __post_init__(self):
        if not self.session_start:
            self.session_start = get_current_timestamp()
        if not self.created_at:
            self.created_at = get_current_timestamp()


@dataclass
class KnowledgeEntity:
    """Knowledge graph entity model."""
    entity_id: str = ""
    name: str = ""
    type: str = ""
    properties: Optional[Dict[str, Any]] = None
    created_at: str = ""
    last_updated: str = ""
    embedding: Optional[bytes] = None
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
        if not self.created_at:
            self.created_at = get_current_timestamp()
        if not self.last_updated:
            self.last_updated = get_current_timestamp()


@dataclass
class KnowledgeRelationship:
    """Knowledge graph relationship model."""
    relationship_id: str = ""
    source_id: str = ""
    target_id: str = ""
    type: str = ""
    properties: Optional[Dict[str, Any]] = None
    confidence: float = 0.5
    valid_from: str = ""
    valid_until: Optional[str] = None
    created_at: str = ""
    last_updated: str = ""
    
    def __post_init__(self):
        if self.properties is None:
            self.properties = {}
        if not self.valid_from:
            self.valid_from = get_current_timestamp()
        if not self.created_at:
            self.created_at = get_current_timestamp()
        if not self.last_updated:
            self.last_updated = get_current_timestamp()


@dataclass
class KnowledgeFact:
    """Knowledge graph fact model."""
    fact_id: str = ""
    user_id: str = ""
    content: str = ""
    source_message_id: Optional[str] = None
    entities: List[str] = None
    relationships: List[str] = None
    confidence: float = 0.5
    importance: float = 0.5
    created_at: str = ""
    valid_from: str = ""
    valid_until: Optional[str] = None
    embedding: Optional[bytes] = None
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = []
        if self.relationships is None:
            self.relationships = []
        if not self.valid_from:
            self.valid_from = get_current_timestamp()
        if not self.created_at:
            self.created_at = get_current_timestamp()


# Database schema definitions
DATABASE_SCHEMA = {
    "user_profile_v4": """
        CREATE TABLE IF NOT EXISTS user_profile_v4 (
            user_id TEXT PRIMARY KEY,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            name TEXT,
            interests TEXT,
            preferred_style TEXT,
            recurring_topics TEXT,
            emotional_patterns TEXT,
            relationship_depth REAL DEFAULT 0.0,
            last_style_analysis_ts TEXT,
            avg_msg_length REAL DEFAULT 50.0,
            emoji_frequency REAL DEFAULT 0.1,
            question_rate REAL DEFAULT 0.2,
            formality_score REAL DEFAULT 0.5,
            conversation_pattern TEXT DEFAULT 'new_user',
            avg_session_gap_hours REAL DEFAULT 24.0,
            preferred_chat_times TEXT DEFAULT '',
            last_session_duration_minutes REAL DEFAULT 0.0,
            total_sessions INTEGER DEFAULT 0,
            longest_gap_days REAL DEFAULT 0.0,
            time_zone TEXT DEFAULT 'UTC',
            session_start_time TEXT DEFAULT '',
            last_active TEXT,
            last_interaction_time TEXT,
            personality_traits TEXT,
            last_updated TEXT,
            created_at TEXT
        )
    """,
    
    "chat_log_v4": """
        CREATE TABLE IF NOT EXISTS chat_log_v4 (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            timestamp TEXT NOT NULL,
            role TEXT NOT NULL CHECK(role IN ('user', 'assistant', 'system')),
            content TEXT NOT NULL,
            emotion_analysis TEXT,
            sentiment TEXT,
            prompted_by_user_log_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES user_profile_v4(user_id) ON DELETE CASCADE,
            FOREIGN KEY (prompted_by_user_log_id) REFERENCES chat_log_v4(id) ON DELETE SET NULL
        )
    """,
    
    "feedback_v4": """
        CREATE TABLE IF NOT EXISTS feedback_v4 (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            chat_log_id INTEGER,
            message_content TEXT,
            rating INTEGER NOT NULL CHECK(rating IN (-1, 1)),
            feedback_type TEXT NOT NULL,
            comment TEXT,
            timestamp TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES user_profile_v4(user_id) ON DELETE CASCADE,
            FOREIGN KEY (chat_log_id) REFERENCES chat_log_v4(id) ON DELETE SET NULL
        )
    """,
    
    "contextual_memory_v4": """
        CREATE TABLE IF NOT EXISTS contextual_memory_v4 (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            text TEXT NOT NULL,
            embedding BLOB NOT NULL,
            emotion TEXT,
            importance REAL DEFAULT 0.5,
            timestamp TEXT NOT NULL,
            last_accessed TEXT,
            access_count INTEGER DEFAULT 0,
            related_chat_log_id INTEGER,
            memory_type TEXT DEFAULT 'short_term',
            FOREIGN KEY (user_id) REFERENCES user_profile_v4(user_id) ON DELETE CASCADE,
            FOREIGN KEY (related_chat_log_id) REFERENCES chat_log_v4(id) ON DELETE SET NULL
        )
    """,
    
    "conversation_sessions": """
        CREATE TABLE IF NOT EXISTS conversation_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            session_start TEXT NOT NULL,
            session_end TEXT,
            message_count INTEGER DEFAULT 0,
            session_duration_minutes REAL DEFAULT 0.0,
            gap_since_last_hours REAL DEFAULT 0.0,
            day_of_week INTEGER,
            hour_of_day INTEGER,
            created_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES user_profile_v4(user_id) ON DELETE CASCADE
        )
    """,

    "user_sessions": """
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT,
            message_count INTEGER DEFAULT 0,
            duration_seconds INTEGER DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES user_profile_v4(user_id) ON DELETE CASCADE
        )
    """
}

# Database indices for performance
DATABASE_INDICES = [
    "CREATE INDEX IF NOT EXISTS idx_chat_user_timestamp ON chat_log_v4 (user_id, timestamp DESC)",
    "CREATE INDEX IF NOT EXISTS idx_feedback_user_type ON feedback_v4 (user_id, feedback_type)",
    "CREATE INDEX IF NOT EXISTS idx_memory_user_timestamp ON contextual_memory_v4 (user_id, timestamp DESC)",
    "CREATE INDEX IF NOT EXISTS idx_memory_user_related_chat ON contextual_memory_v4 (user_id, related_chat_log_id)",
    "CREATE INDEX IF NOT EXISTS idx_chat_prompted_by ON chat_log_v4 (prompted_by_user_log_id)",
    "CREATE INDEX IF NOT EXISTS idx_memory_type_importance ON contextual_memory_v4 (user_id, memory_type, importance DESC)",
    "CREATE INDEX IF NOT EXISTS idx_memory_text_search ON contextual_memory_v4 (user_id, text)",
    "CREATE INDEX IF NOT EXISTS idx_sessions_user_start ON user_sessions (user_id, start_time DESC)",
    "CREATE INDEX IF NOT EXISTS idx_sessions_timing ON conversation_sessions (user_id, day_of_week, hour_of_day)"
]
