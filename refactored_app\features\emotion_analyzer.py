"""
Advanced Emotion Analysis System.
Analyzes text for detailed emotions and sentiment using transformer models.
"""

from typing import Any, Dict, List, Optional

# Import heavy libs lazily inside loaders to avoid test-time import explosions
import torch

try:
    from ..config import config
    from ..core import LoggerMixin
except ImportError:
    from config import config
    from core import LoggerMixin


class AdvancedEmotionAnalyzer(LoggerMixin):
    """
    Analyzes text for detailed emotions and overall sentiment using HF Transformers.
    Handles model loading errors and provides fallback mechanisms.
    """
    
    def __init__(self, emotion_model_name: Optional[str] = None, 
                 sentiment_model_name: Optional[str] = None):
        """Initialize emotion analyzer with transformer models."""
        self.emotion_model_name = emotion_model_name or config.emotion.model_name
        self.sentiment_model_name = sentiment_model_name or config.emotion.sentiment_model
        
        # Initialize models
        self.emotion_pipeline = None
        self.sentiment_pipeline = None
        
        # Try to load models; on failure, stay in fallback mode (no blocking)
        self._load_emotion_model()
        self._load_sentiment_model()

        # Fallback emotion mapping
        self.emotion_keywords = {
            'joy': ['happy', 'excited', 'thrilled', 'delighted', 'cheerful', 'elated'],
            'sadness': ['sad', 'depressed', 'down', 'melancholy', 'gloomy', 'dejected'],
            'anger': ['angry', 'furious', 'mad', 'irritated', 'annoyed', 'frustrated'],
            'fear': ['scared', 'afraid', 'terrified', 'anxious', 'worried', 'nervous'],
            'surprise': ['surprised', 'shocked', 'amazed', 'astonished', 'stunned'],
            'disgust': ['disgusted', 'revolted', 'repulsed', 'sickened'],
            'love': ['love', 'adore', 'cherish', 'treasure', 'devoted'],
            'optimism': ['hopeful', 'optimistic', 'positive', 'confident'],
            'pessimism': ['hopeless', 'pessimistic', 'negative', 'doubtful']
        }
        
        self.logger.info("Advanced emotion analyzer initialized")
    
    def _load_emotion_model(self) -> None:
        """Load emotion classification model."""
        try:
            self.logger.info(f"Loading emotion model: {self.emotion_model_name}")

            # Lazy import to avoid bringing in TF/JAX during test collection
            from transformers import AutoModelForSequenceClassification, AutoTokenizer, pipeline

            # Load tokenizer and model
            tokenizer = AutoTokenizer.from_pretrained(self.emotion_model_name)
            model = AutoModelForSequenceClassification.from_pretrained(self.emotion_model_name)

            # Create pipeline with GPU support if available
            device = 0 if torch.cuda.is_available() else -1
            self.emotion_pipeline = pipeline(
                "text-classification",
                model=model,
                tokenizer=tokenizer,
                top_k=None,
                device=device
            )

            device_name = "GPU" if device == 0 else "CPU"
            self.logger.info(f"Emotion model loaded successfully on {device_name}")
            
        except Exception as e:
            # Do not let transformer import/version issues stall the app/tests; use fallback
            self.logger.warning(f"Emotion model unavailable, falling back to keywords: {e}")
            self.emotion_pipeline = None

    def _load_sentiment_model(self) -> None:
        """Load sentiment analysis model."""
        try:
            self.logger.info(f"Loading sentiment model: {self.sentiment_model_name}")

            # Lazy import to avoid bringing in TF/JAX during test collection
            from transformers import pipeline

            # Create sentiment pipeline
            device = 0 if torch.cuda.is_available() else -1
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=self.sentiment_model_name,
                device=device
            )

            device_name = "GPU" if device == 0 else "CPU"
            self.logger.info(f"Sentiment model loaded successfully on {device_name}")
            
        except Exception as e:
            # Do not let transformer import/version issues stall the app/tests; use fallback
            self.logger.warning(f"Sentiment model unavailable, using simple fallback: {e}")
            self.sentiment_pipeline = None

    def analyze_emotion(self, text: str) -> Dict[str, Any]:
        """
        Analyze emotions in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with emotion analysis results
        """
        try:
            if not text or not text.strip():
                return self._get_default_emotion_result()
            
            # Clean text
            cleaned_text = text.strip()[:512]  # Limit length for model
            
            # Try transformer model first
            if self.emotion_pipeline:
                try:
                    results = self.emotion_pipeline(cleaned_text)
                    return self._process_emotion_results(results, cleaned_text)
                except Exception as e:
                    self.logger.warning(f"Emotion pipeline failed: {e}")
            
            # Fallback to keyword-based analysis
            return self._fallback_emotion_analysis(cleaned_text)
            
        except Exception as e:
            self.logger.error(f"Error in emotion analysis: {e}")
            return self._get_default_emotion_result()
    
    def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment in text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment analysis results
        """
        try:
            if not text or not text.strip():
                return self._get_default_sentiment_result()
            
            # Clean text
            cleaned_text = text.strip()[:512]
            
            # Try transformer model first
            if self.sentiment_pipeline:
                try:
                    results = self.sentiment_pipeline(cleaned_text)
                    return self._process_sentiment_results(results)
                except Exception as e:
                    self.logger.warning(f"Sentiment pipeline failed: {e}")
            
            # Fallback to simple sentiment analysis
            return self._fallback_sentiment_analysis(cleaned_text)
            
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return self._get_default_sentiment_result()
    
    def analyze_comprehensive(self, text: str) -> Dict[str, Any]:
        """
        Perform comprehensive emotion and sentiment analysis.
        
        Args:
            text: Text to analyze
            
        Returns:
            Combined analysis results
        """
        emotion_result = self.analyze_emotion(text)
        sentiment_result = self.analyze_sentiment(text)
        
        return {
            'emotion': emotion_result,
            'sentiment': sentiment_result,
            'combined_score': self._calculate_combined_score(emotion_result, sentiment_result)
        }
    
    def _process_emotion_results(self, results: List[Dict], text: str) -> Dict[str, Any]:
        """Process emotion model results."""
        if not results:
            return self._get_default_emotion_result()
        
        # Get top emotion
        top_emotion = max(results, key=lambda x: x['score'])
        
        # Calculate intensity based on confidence and text features
        intensity = self._calculate_emotion_intensity(top_emotion['score'], text)
        
        # Get all emotions above threshold
        significant_emotions = [
            r for r in results 
            if r['score'] > 0.1
        ]
        
        return {
            'primary_emotion': top_emotion['label'].lower(),
            'intensity': intensity,
            'confidence': top_emotion['score'],
            'all_emotions': {r['label'].lower(): r['score'] for r in significant_emotions}
        }
    
    def _process_sentiment_results(self, results: List[Dict]) -> Dict[str, Any]:
        """Process sentiment model results."""
        if not results:
            return self._get_default_sentiment_result()
        
        result = results[0]
        label = result['label'].lower()
        score = result['score']
        
        # Normalize sentiment labels
        if label in ['positive', 'pos']:
            sentiment = 'positive'
        elif label in ['negative', 'neg']:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'confidence': score,
            'polarity': score if sentiment == 'positive' else -score if sentiment == 'negative' else 0.0
        }
    
    def _fallback_emotion_analysis(self, text: str) -> Dict[str, Any]:
        """Fallback emotion analysis using keywords."""
        text_lower = text.lower()
        emotion_scores = {}
        
        # Check for emotion keywords
        for emotion, keywords in self.emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = min(1.0, score * 0.3)
        
        if emotion_scores:
            primary_emotion = max(emotion_scores.keys(), key=lambda k: emotion_scores[k])
            intensity = emotion_scores[primary_emotion]
        else:
            primary_emotion = 'neutral'
            intensity = 0.5
        
        return {
            'primary_emotion': primary_emotion,
            'intensity': intensity,
            'confidence': 0.6,  # Lower confidence for fallback
            'all_emotions': emotion_scores
        }
    
    def _fallback_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """Fallback sentiment analysis using simple heuristics."""
        text_lower = text.lower()
        
        positive_words = ['good', 'great', 'awesome', 'amazing', 'wonderful', 'excellent', 'love', 'like', 'happy', 'excited']
        negative_words = ['bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'sad', 'angry', 'frustrated', 'disappointed']
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            sentiment = 'positive'
            confidence = min(0.8, positive_count * 0.2)
        elif negative_count > positive_count:
            sentiment = 'negative'
            confidence = min(0.8, negative_count * 0.2)
        else:
            sentiment = 'neutral'
            confidence = 0.5
        
        polarity = confidence if sentiment == 'positive' else -confidence if sentiment == 'negative' else 0.0
        
        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'polarity': polarity
        }
    
    def _calculate_emotion_intensity(self, confidence: float, text: str) -> float:
        """Calculate emotion intensity based on confidence and text features."""
        intensity = confidence
        
        # Boost for exclamation marks
        exclamation_count = text.count('!')
        intensity += min(0.2, exclamation_count * 0.05)
        
        # Boost for capital letters (indicating strong emotion)
        caps_ratio = sum(1 for c in text if c.isupper()) / max(1, len(text))
        if caps_ratio > 0.3:
            intensity += 0.1
        
        # Boost for emotional intensifiers
        intensifiers = ['very', 'extremely', 'incredibly', 'absolutely', 'totally', 'completely']
        if any(word in text.lower() for word in intensifiers):
            intensity += 0.1
        
        return min(1.0, intensity)
    
    def _calculate_combined_score(self, emotion_result: Dict, sentiment_result: Dict) -> float:
        """Calculate combined emotional score."""
        emotion_intensity = emotion_result.get('intensity', 0.5)
        sentiment_polarity = abs(sentiment_result.get('polarity', 0.0))
        
        # Combine emotion intensity and sentiment strength
        combined = (emotion_intensity + sentiment_polarity) / 2
        return min(1.0, combined)
    
    def _get_default_emotion_result(self) -> Dict[str, Any]:
        """Get default emotion result."""
        return {
            'primary_emotion': 'neutral',
            'intensity': 0.5,
            'confidence': 0.5,
            'all_emotions': {'neutral': 0.5}
        }
    
    def _get_default_sentiment_result(self) -> Dict[str, Any]:
        """Get default sentiment result."""
        return {
            'sentiment': 'neutral',
            'confidence': 0.5,
            'polarity': 0.0
        }
    
    def get_empathy_response(self, emotion_result: Dict[str, Any]) -> Optional[str]:
        """Generate empathetic response based on emotion analysis."""
        try:
            primary_emotion = emotion_result.get('primary_emotion', 'neutral')
            intensity = emotion_result.get('intensity', 0.5)
            
            # Get appropriate empathy statements
            empathy_statements = config.emotion.empathy_statements
            
            if primary_emotion in ['sadness', 'fear', 'anger'] and intensity > 0.6:
                responses = empathy_statements.get('validate', []) + empathy_statements.get('support', [])
            elif primary_emotion in ['joy', 'love', 'optimism'] and intensity > 0.6:
                responses = empathy_statements.get('celebrate', []) + empathy_statements.get('share_joy', [])
            else:
                responses = empathy_statements.get('acknowledge', [])
            
            if responses:
                import random
                return random.choice(responses)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error generating empathy response: {e}")
            return None
