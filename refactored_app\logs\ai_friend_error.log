2025-09-10 01:28:32 | ERROR    | 25864 | 27736 | main.py:159 | main | Application failed to start: Cannot find empty port in range: 7860-7860. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\refactored_app\main.py", line 147, in main
    app.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7860-7860. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 01:38:04 | ERROR    | 21040 | 21164 | main.py:159 | main | Application failed to start: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\refactored_app\main.py", line 147, in main
    app.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 01:39:08 | ERROR    | 24400 | 16268 | main.py:159 | main | Application failed to start: [WinError 10061] No connection could be made because the target machine actively refused it
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_transports\default.py", line 66, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_transports\default.py", line 228, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_sync\connection_pool.py", line 256, in handle_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_sync\connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_sync\connection.py", line 101, in handle_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_sync\connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_sync\connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_backends\sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "C:\ProgramData\miniconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [WinError 10061] No connection could be made because the target machine actively refused it

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\refactored_app\main.py", line 147, in main
    app.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2408, in launch
    httpx.get(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_api.py", line 189, in get
    return request(
           ^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_api.py", line 100, in request
    return client.request(
           ^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_client.py", line 814, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_client.py", line 901, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_client.py", line 929, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_client.py", line 966, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_client.py", line 1002, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_transports\default.py", line 227, in handle_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\miniconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\httpx\_transports\default.py", line 83, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [WinError 10061] No connection could be made because the target machine actively refused it
2025-09-10 02:47:43 | ERROR    | 4576 | 23712 | main.py:159 | main | Application failed to start: Cannot find empty port in range: 7865-7865. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\refactored_app\main.py", line 147, in main
    app.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7865-7865. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
