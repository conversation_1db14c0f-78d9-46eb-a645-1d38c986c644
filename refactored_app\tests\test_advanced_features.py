"""
Tests for advanced features in the refactored AI Friend application.
Tests emotion analysis, interest tracking, news fetching, and FAISS memory.
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock

from refactored_app.database import DatabaseManager
from refactored_app.features import (
    AdvancedEmotionAnalyzer,
    IntelligentInterestTracker,
    NewsFetcher,
    RateLimiter,
    ConversationRateLimiter
)
from refactored_app.memory import FAISSMemoryManager


class TestAdvancedEmotionAnalyzer:
    """Test emotion analysis functionality."""
    
    @pytest.fixture
    def emotion_analyzer(self):
        """Create emotion analyzer for testing."""
        return AdvancedEmotionAnalyzer()
    
    def test_emotion_analysis_fallback(self, emotion_analyzer):
        """Test fallback emotion analysis when models aren't available."""
        # Test with emotional text
        result = emotion_analyzer.analyze_emotion("I'm so happy and excited!")
        
        assert 'primary_emotion' in result
        assert 'intensity' in result
        assert 'confidence' in result
        assert isinstance(result['intensity'], float)
        assert 0.0 <= result['intensity'] <= 1.0
    
    def test_sentiment_analysis_fallback(self, emotion_analyzer):
        """Test fallback sentiment analysis."""
        # Test positive sentiment
        result = emotion_analyzer.analyze_sentiment("This is amazing and wonderful!")
        
        assert 'sentiment' in result
        assert 'confidence' in result
        assert 'polarity' in result
        assert result['sentiment'] in ['positive', 'negative', 'neutral']
    
    def test_comprehensive_analysis(self, emotion_analyzer):
        """Test comprehensive emotion and sentiment analysis."""
        result = emotion_analyzer.analyze_comprehensive("I'm feeling really sad today")
        
        assert 'emotion' in result
        assert 'sentiment' in result
        assert 'combined_score' in result
        assert isinstance(result['combined_score'], float)
    
    def test_empathy_response(self, emotion_analyzer):
        """Test empathy response generation."""
        emotion_result = {
            'primary_emotion': 'sadness',
            'intensity': 0.8,
            'confidence': 0.7
        }
        
        response = emotion_analyzer.get_empathy_response(emotion_result)
        # Response might be None if no empathy statements configured
        if response:
            assert isinstance(response, str)
            assert len(response) > 0


class TestIntelligentInterestTracker:
    """Test interest tracking functionality."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        yield db_path
        
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    @pytest.fixture
    def interest_tracker(self, temp_db):
        """Create interest tracker with temp database."""
        db_manager = DatabaseManager(temp_db)
        return IntelligentInterestTracker(db_manager)
    
    def test_interest_extraction(self, interest_tracker):
        """Test interest extraction from text."""
        text = "I love programming and coding. I'm really into AI and machine learning."
        user_id = "test_user"
        
        interests = interest_tracker.extract_interests_from_text(text, user_id)
        
        assert isinstance(interests, list)
        # Should detect technology-related interests
        tech_interests = [i for i in interests if i.get('category') == 'technology']
        assert len(tech_interests) > 0
    
    def test_get_user_interests(self, interest_tracker):
        """Test retrieving user interests."""
        user_id = "test_user"
        
        # First extract some interests
        interest_tracker.extract_interests_from_text("I love music and movies", user_id)
        
        # Then retrieve them
        interests = interest_tracker.get_user_interests(user_id)
        
        assert isinstance(interests, list)
    
    def test_interest_summary(self, interest_tracker):
        """Test interest summary generation."""
        user_id = "test_user"
        
        # Add some interests
        interest_tracker.extract_interests_from_text("I enjoy cooking and traveling", user_id)
        
        summary = interest_tracker.get_interest_summary(user_id)
        
        assert isinstance(summary, dict)
        assert 'top_interests' in summary
        assert 'by_category' in summary
        assert 'total_interests' in summary


class TestNewsFetcher:
    """Test news fetching functionality."""
    
    @pytest.fixture
    def news_fetcher(self):
        """Create news fetcher for testing."""
        return NewsFetcher()
    
    def test_news_fetcher_initialization(self, news_fetcher):
        """Test news fetcher initializes correctly."""
        assert news_fetcher.cache == {}
        assert news_fetcher.session is not None
    
    @patch('refactored_app.features.news_fetcher.search')
    def test_fetch_news_with_mock(self, mock_search, news_fetcher):
        """Test news fetching with mocked search."""
        # Mock search results
        mock_search.return_value = [
            'https://example.com/news1',
            'https://example.com/news2'
        ]
        
        # Mock requests
        with patch.object(news_fetcher.session, 'get') as mock_get:
            mock_response = MagicMock()
            mock_response.headers = {'Content-Type': 'text/html'}
            mock_response.content = b'<html><head><title>Test News Title</title></head></html>'
            mock_response.raise_for_status.return_value = None
            mock_get.return_value = mock_response
            
            headlines = news_fetcher.fetch_news("technology", num_results=2)
            
            assert isinstance(headlines, list)
    
    def test_cache_functionality(self, news_fetcher):
        """Test news caching functionality."""
        # Test cache stats
        stats = news_fetcher.get_cache_stats()
        assert 'total_entries' in stats
        assert 'expired_entries' in stats
        
        # Test cache clearing
        news_fetcher.clear_cache()
        assert len(news_fetcher.cache) == 0


class TestRateLimiter:
    """Test rate limiting functionality."""
    
    @pytest.fixture
    def rate_limiter(self):
        """Create rate limiter for testing."""
        return RateLimiter(requests_per_minute=10, burst_size=5)
    
    def test_rate_limiter_initialization(self, rate_limiter):
        """Test rate limiter initializes correctly."""
        assert rate_limiter.requests_per_minute == 10
        assert rate_limiter.burst_size == 5
        assert rate_limiter.refill_rate > 0
    
    def test_rate_limit_check(self, rate_limiter):
        """Test rate limit checking."""
        user_id = "test_user"
        
        # First request should succeed
        assert rate_limiter.check_rate_limit(user_id, "test_action") == True
        
        # Check remaining tokens
        remaining = rate_limiter.get_remaining_tokens(user_id)
        assert remaining < 5  # Should be less than initial burst size
    
    def test_user_stats(self, rate_limiter):
        """Test user statistics."""
        user_id = "test_user"
        
        # Make a request
        rate_limiter.check_rate_limit(user_id, "test")
        
        # Get stats
        stats = rate_limiter.get_user_stats(user_id)
        
        assert 'remaining_tokens' in stats
        assert 'time_until_next_token' in stats
        assert 'requests_last_hour' in stats
        assert 'requests_last_minute' in stats
    
    def test_conversation_rate_limiter(self):
        """Test conversation-specific rate limiter."""
        conv_limiter = ConversationRateLimiter()
        user_id = "test_user"
        message = "Hello, this is a test message"
        
        # Should allow normal message
        assert conv_limiter.check_message_rate_limit(user_id, message) == True
        
        # Get message stats
        stats = conv_limiter.get_message_stats(user_id)
        assert 'total_chars_this_hour' in stats
        assert 'messages_this_hour' in stats


@pytest.mark.skipif(
    os.environ.get('SKIP_FAISS_TESTS') == 'true',
    reason="FAISS tests skipped (set SKIP_FAISS_TESTS=true to skip)"
)
class TestFAISSMemoryManager:
    """Test FAISS memory management functionality."""
    
    @pytest.fixture
    def temp_db(self):
        """Create temporary database."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        yield db_path
        
        if os.path.exists(db_path):
            os.unlink(db_path)
    
    @pytest.fixture
    def faiss_memory_manager(self, temp_db):
        """Create FAISS memory manager with temp database."""
        try:
            db_manager = DatabaseManager(temp_db)
            return FAISSMemoryManager(db_manager)
        except Exception as e:
            pytest.skip(f"FAISS not available: {e}")
    
    def test_memory_storage(self, faiss_memory_manager):
        """Test storing memories with FAISS."""
        user_id = "test_user"
        text = "I love programming and building cool applications"
        
        memory_id = faiss_memory_manager.store_memory(
            user_id=user_id,
            text=text,
            emotion="joy",
            importance=0.8
        )
        
        assert memory_id is not None
        assert memory_id > 0
    
    def test_memory_recall(self, faiss_memory_manager):
        """Test recalling memories with FAISS."""
        user_id = "test_user"
        
        # Store a memory
        faiss_memory_manager.store_memory(
            user_id=user_id,
            text="I enjoy coding in Python",
            emotion="joy",
            importance=0.7
        )
        
        # Recall memories
        memories = faiss_memory_manager.recall_memories(
            user_id=user_id,
            query_text="programming",
            max_memories=5
        )
        
        assert isinstance(memories, list)
    
    def test_memory_consolidation(self, faiss_memory_manager):
        """Test memory consolidation."""
        user_id = "test_user"
        
        # Store multiple memories
        for i in range(5):
            faiss_memory_manager.store_memory(
                user_id=user_id,
                text=f"Test memory {i}",
                emotion="neutral",
                importance=0.3 + (i * 0.1)
            )
        
        # Test consolidation (should not raise errors)
        faiss_memory_manager.consolidate_memories(user_id)


class TestIntegratedFeatures:
    """Test integration of all advanced features."""

    @pytest.fixture
    def temp_db(self):
        """Create temporary database."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name

        yield db_path

        if os.path.exists(db_path):
            os.unlink(db_path)

    @pytest.fixture
    def integrated_system(self, temp_db):
        """Create integrated system with all features."""
        from refactored_app.database import DatabaseManager
        from refactored_app.features import (
            PersonalityEngine, RelationshipManager, TimeAwarenessManager,
            FestivalTracker, ResponseHumanizer
        )
        from refactored_app.config import config

        db_manager = DatabaseManager(temp_db)

        return {
            'db_manager': db_manager,
            'personality_engine': PersonalityEngine(config.persona.__dict__, db_manager),
            'relationship_manager': RelationshipManager(db_manager),
            'time_awareness': TimeAwarenessManager(db_manager),
            'festival_tracker': FestivalTracker(),
            'response_humanizer': ResponseHumanizer()
        }

    def test_personality_adaptation(self, integrated_system):
        """Test personality adaptation based on user interaction."""
        personality_engine = integrated_system['personality_engine']
        user_id = "test_user"

        # Simulate vulnerable interaction
        interaction_data = {
            'user_emotion': 'sadness',
            'user_sentiment': 'negative',
            'vulnerability_score': 0.8,
            'reciprocity_signal': True,
            'conversation_length': 50,
            'emotional_intensity': 0.7
        }

        # Get initial personality
        initial_traits = personality_engine.get_current_personality(user_id)

        # Adapt personality
        personality_engine.adapt_personality(user_id, interaction_data)

        # Get updated personality
        updated_traits = personality_engine.get_current_personality(user_id)

        # Warmth and empathy should have increased
        assert updated_traits['warmth'] >= initial_traits['warmth']
        assert updated_traits['empathy'] >= initial_traits['empathy']

    def test_relationship_progression(self, integrated_system):
        """Test relationship depth progression."""
        relationship_manager = integrated_system['relationship_manager']
        user_id = "test_user"

        # Start with acquaintance
        initial_stage, initial_depth = relationship_manager.get_relationship_stage_and_depth(user_id)
        assert initial_stage == "acquaintance"
        assert initial_depth == 0.0

        # Simulate multiple positive interactions
        for i in range(5):
            interaction_data = {
                'vulnerability_score': 0.6,
                'message_length': 100,
                'reciprocity_signal': True,
                'emotional_intensity': 0.5
            }
            relationship_manager.update_relationship_depth(user_id, interaction_data)

        # Check progression
        final_stage, final_depth = relationship_manager.get_relationship_stage_and_depth(user_id)
        assert final_depth > initial_depth
        # Should progress to at least casual friend
        assert final_stage in ["casual friend", "friend", "close friend"]

    def test_time_awareness_session_tracking(self, integrated_system):
        """Test time awareness and session tracking."""
        time_awareness = integrated_system['time_awareness']
        user_id = "test_user"

        # Start session
        session_id = time_awareness.start_session(user_id)
        assert session_id is not None

        # Update activity
        time_awareness.update_session_activity(user_id)

        # Get time context
        context = time_awareness.get_time_context(user_id)
        assert 'conversation_pattern' in context
        assert 'time_gap_category' in context

        # End session
        time_awareness.end_session(user_id)

    def test_festival_awareness(self, integrated_system):
        """Test festival tracking and awareness."""
        festival_tracker = integrated_system['festival_tracker']

        # Get festival context
        context = festival_tracker.get_festival_context()
        assert 'today_festival' in context
        assert 'upcoming_festivals' in context

        # Test festival season detection
        is_season = festival_tracker.is_festival_season()
        assert isinstance(is_season, bool)

        # Test festival summary
        summary = festival_tracker.get_festival_summary()
        assert 'total_festivals' in summary
        assert isinstance(summary['total_festivals'], int)

    def test_response_humanization(self, integrated_system):
        """Test response humanization."""
        response_humanizer = integrated_system['response_humanizer']

        # Test basic humanization
        original_response = "I am very happy to help you with this task."
        style_params = {
            'warmth_level': 0.8,
            'humor_level': 6,
            'formality_level': 0.3,
            'use_emojis': True,
            'use_slang': True
        }

        humanized = response_humanizer.humanize_response(original_response, style_params)

        # Should be different from original (more casual)
        assert humanized != original_response
        assert len(humanized) > 0

        # Test empathy addition
        empathy_response = response_humanizer.add_empathy_response(
            "That sounds difficult", "sadness"
        )
        assert len(empathy_response) > len("That sounds difficult")

    def test_full_integration_workflow(self, integrated_system):
        """Test complete workflow with all features."""
        user_id = "integration_test_user"

        # Simulate a conversation workflow
        personality_engine = integrated_system['personality_engine']
        relationship_manager = integrated_system['relationship_manager']
        time_awareness = integrated_system['time_awareness']
        response_humanizer = integrated_system['response_humanizer']

        # 1. Start session
        time_awareness.start_session(user_id)

        # 2. Process user message with emotion and vulnerability
        interaction_data = {
            'user_emotion': 'joy',
            'user_sentiment': 'positive',
            'vulnerability_score': 0.4,
            'reciprocity_signal': True,
            'conversation_length': 30,
            'emotional_intensity': 0.6
        }

        # 3. Update relationship and personality
        new_depth = relationship_manager.update_relationship_depth(user_id, interaction_data)
        personality_engine.adapt_personality(user_id, interaction_data)

        # 4. Get current state
        stage, depth = relationship_manager.get_relationship_stage_and_depth(user_id)
        personality = personality_engine.get_current_personality(user_id)

        # 5. Generate style parameters and humanize response
        style_params = personality_engine.get_style_parameters(user_id, stage)

        test_response = "That's wonderful! I'm so happy for you!"
        humanized = response_humanizer.humanize_response(test_response, style_params)

        # 6. End session
        time_awareness.end_session(user_id)

        # Verify everything worked
        assert new_depth >= 0.0
        assert stage in ["acquaintance", "casual friend", "friend", "close friend"]
        assert isinstance(personality, dict)
        assert len(style_params) > 0
        assert len(humanized) > 0


if __name__ == "__main__":
    pytest.main([__file__])
