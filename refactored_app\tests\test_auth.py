import unittest
from unittest.mock import <PERSON><PERSON>ock, patch
from auth.manager import Authentication<PERSON>anager
from database.manager import DatabaseManager
from itsdangerous import URLSafeTimedSerializer

class TestAuthenticationManager(unittest.TestCase):

    def setUp(self):
        self.db_manager = MagicMock(spec=DatabaseManager)
        with patch('auth.manager.config') as mock_config:
            mock_config.security.secret_key = 'test-secret-key'
            mock_config.security.password_min_length = 8
            self.auth_manager = AuthenticationManager(self.db_manager)

    def test_register_user_success(self):
        self.db_manager.get_user_by_username.return_value = None
        self.db_manager.create_user.return_value = True

        success, message, token = self.auth_manager.register_user("testuser", "password123", "Test User")

        self.assertTrue(success)
        self.assertEqual(message, "Registration successful")
        self.assertIsNotNone(token)
        user_id = self.auth_manager.get_user_id_from_token(token)
        self.assertIsNotNone(user_id)

    def test_register_user_already_exists(self):
        self.db_manager.get_user_by_username.return_value = {'user_id': '123', 'username': 'testuser'}

        success, message, token = self.auth_manager.register_user("testuser", "password123")

        self.assertFalse(success)
        self.assertEqual(message, "Registration failed. Please try a different username.")
        self.assertIsNone(token)

    def test_login_success(self):
        hashed_password = self.auth_manager._hash_password("password123")
        self.db_manager.get_user_by_username.return_value = {
            'user_id': '123',
            'username': 'testuser',
            'password_hash': hashed_password
        }

        success, token, message = self.auth_manager.authenticate_user("testuser", "password123")

        self.assertTrue(success)
        self.assertEqual(message, "Login successful")
        self.assertIsNotNone(token)
        user_id = self.auth_manager.get_user_id_from_token(token)
        self.assertEqual(user_id, '123')

    def test_login_user_not_found(self):
        self.db_manager.get_user_by_username.return_value = None

        success, token, message = self.auth_manager.authenticate_user("nonexistentuser", "password123")

        self.assertFalse(success)
        self.assertEqual(message, "Invalid username or password")
        self.assertIsNone(token)

    def test_login_invalid_password(self):
        hashed_password = self.auth_manager._hash_password("password123")
        self.db_manager.get_user_by_username.return_value = {
            'user_id': '123',
            'username': 'testuser',
            'password_hash': hashed_password
        }

        success, token, message = self.auth_manager.authenticate_user("testuser", "wrongpassword")

        self.assertFalse(success)
        self.assertEqual(message, "Invalid username or password")
        self.assertIsNone(token)

    def test_get_user_id_from_token_valid(self):
        token = self.auth_manager._create_session_token('123')
        user_id = self.auth_manager.get_user_id_from_token(token)
        self.assertEqual(user_id, '123')

    def test_get_user_id_from_token_expired(self):
        with patch('auth.manager.config') as mock_config:
            mock_config.security.rate_limit_window = -1 # Expired token
            self.auth_manager = AuthenticationManager(self.db_manager)
            token = self.auth_manager._create_session_token('123')
            user_id = self.auth_manager.get_user_id_from_token(token)
            self.assertIsNone(user_id)

    def test_get_user_id_from_token_invalid(self):
        user_id = self.auth_manager.get_user_id_from_token("invalidtoken")
        self.assertIsNone(user_id)

if __name__ == '__main__':
    unittest.main()