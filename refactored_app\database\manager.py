"""
Database manager for AI Friend application.
Handles database connections, schema setup, and basic operations.
"""

import sqlite3
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple

try:
    from ..config import config
    from ..core import DatabaseError, LoggerMixin, get_current_timestamp, safe_json_dumps, safe_json_loads
    from .models import DATABASE_SCHEMA, DATABASE_INDICES
except ImportError:
    from config import config
    from core import DatabaseError, LoggerMixin, get_current_timestamp, safe_json_dumps, safe_json_loads
    from database.models import DATABASE_SCHEMA, DATABASE_INDICES


class DatabaseManager(LoggerMixin):
    """
    Manages database connections and operations.
    Simplified approach without connection pooling for SQLite.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path or config.database.name
        self.logger.info(f"Initializing database manager for {self.db_path}")
        self._setup_database()
    
    @contextmanager
    def get_connection(self):
        """
        Get a database connection with proper error handling.
        
        Yields:
            SQLite connection object
        """
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=config.database.connection_timeout,
                isolation_level=None  # Autocommit mode
            )
            
            # Configure connection
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Optimize SQLite settings
            cursor.execute("PRAGMA journal_mode=WAL")
            cursor.execute(f"PRAGMA busy_timeout={config.database.busy_timeout}")
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.execute(f"PRAGMA cache_size={config.database.cache_size}")
            cursor.execute("PRAGMA synchronous=NORMAL")
            cursor.close()
            
            # Begin transaction
            conn.execute("BEGIN")
            yield conn
            conn.execute("COMMIT")
            
        except sqlite3.Error as e:
            self.logger.error(f"Database error: {e}")
            if conn:
                try:
                    conn.execute("ROLLBACK")
                except sqlite3.Error:
                    pass
            raise DatabaseError(f"Database operation failed: {e}")
        
        except Exception as e:
            self.logger.error(f"Unexpected database error: {e}")
            if conn:
                try:
                    conn.execute("ROLLBACK")
                except sqlite3.Error:
                    pass
            raise DatabaseError(f"Unexpected database error: {e}")
        
        finally:
            if conn:
                conn.close()
    
    def _setup_database(self) -> None:
        """Create database schema and indices."""
        self.logger.info("Setting up database schema")
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create tables
                for table_name, schema in DATABASE_SCHEMA.items():
                    cursor.execute(schema)
                    self.logger.debug(f"Created/verified table: {table_name}")
                
                # Create indices
                for index_sql in DATABASE_INDICES:
                    cursor.execute(index_sql)
                
                self.logger.info("Database schema setup complete")
                
        except Exception as e:
            self.logger.error(f"Failed to setup database: {e}")
            raise DatabaseError(f"Database setup failed: {e}")
    
    def execute_query(self, query: str, params: Tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of result dictionaries
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        
        except Exception as e:
            self.logger.error(f"Query execution failed: {e}")
            raise DatabaseError(f"Query failed: {e}")
    
    def execute_update(self, query: str, params: Tuple = ()) -> int:
        """
        Execute an INSERT/UPDATE/DELETE query.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Number of affected rows
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.rowcount
        
        except Exception as e:
            self.logger.error(f"Update execution failed: {e}")
            raise DatabaseError(f"Update failed: {e}")
    
    def execute_insert(self, query: str, params: Tuple = ()) -> int:
        """
        Execute an INSERT query and return the last row ID.
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            Last inserted row ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                return cursor.lastrowid or 0
        
        except Exception as e:
            self.logger.error(f"Insert execution failed: {e}")
            raise DatabaseError(f"Insert failed: {e}")
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile by ID."""
        query = f"SELECT * FROM {config.database.user_table} WHERE user_id = ?"
        results = self.execute_query(query, (user_id,))
        
        if results:
            user = results[0]
            # Parse JSON fields
            user['interests'] = safe_json_loads(user.get('interests'), [])
            user['recurring_topics'] = safe_json_loads(user.get('recurring_topics'), [])
            user['emotional_patterns'] = safe_json_loads(user.get('emotional_patterns'), {})
            return user
        
        return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user profile by username."""
        query = f"SELECT * FROM {config.database.user_table} WHERE username = ?"
        results = self.execute_query(query, (username,))
        return results[0] if results else None
    
    def create_user(self, user_data: Dict[str, Any]) -> str:
        """
        Create a new user profile.
        
        Args:
            user_data: User profile data
            
        Returns:
            User ID of created user
        """
        # Serialize JSON fields
        user_data = user_data.copy()
        user_data['interests'] = safe_json_dumps(user_data.get('interests', []))
        user_data['recurring_topics'] = safe_json_dumps(user_data.get('recurring_topics', []))
        user_data['emotional_patterns'] = safe_json_dumps(user_data.get('emotional_patterns', {}))
        
        # Set timestamps
        timestamp = get_current_timestamp()
        user_data.setdefault('created_at', timestamp)
        user_data.setdefault('last_active', timestamp)
        
        # Build query
        columns = list(user_data.keys())
        placeholders = ', '.join(['?' for _ in columns])
        query = f"""
            INSERT INTO {config.database.user_table} 
            ({', '.join(columns)}) 
            VALUES ({placeholders})
        """
        
        self.execute_insert(query, tuple(user_data.values()))
        return user_data['user_id']
    
    def update_user(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update user profile.
        
        Args:
            user_id: User ID to update
            updates: Fields to update
            
        Returns:
            True if update was successful
        """
        if not updates:
            return False
        
        # Serialize JSON fields
        updates = updates.copy()
        if 'interests' in updates:
            updates['interests'] = safe_json_dumps(updates['interests'])
        if 'recurring_topics' in updates:
            updates['recurring_topics'] = safe_json_dumps(updates['recurring_topics'])
        if 'emotional_patterns' in updates:
            updates['emotional_patterns'] = safe_json_dumps(updates['emotional_patterns'])
        
        # Update last_active
        updates['last_active'] = get_current_timestamp()
        
        # Build query
        set_clause = ', '.join([f"{key} = ?" for key in updates.keys()])
        query = f"UPDATE {config.database.user_table} SET {set_clause} WHERE user_id = ?"
        
        affected_rows = self.execute_update(query, tuple(list(updates.values()) + [user_id]))
        return affected_rows > 0
    
    def get_recent_messages(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent chat messages for a user."""
        query = f"""
            SELECT id, role, content, timestamp, prompted_by_user_log_id
            FROM {config.database.chat_table}
            WHERE user_id = ?
            ORDER BY timestamp DESC
            LIMIT ?
        """
        
        results = self.execute_query(query, (user_id, limit))
        return list(reversed(results))  # Return in chronological order
    
    def save_message(self, message_data: Dict[str, Any]) -> int:
        """
        Save a chat message.
        
        Args:
            message_data: Message data
            
        Returns:
            Message ID
        """
        # Serialize emotion analysis
        if 'emotion_analysis' in message_data and message_data['emotion_analysis']:
            message_data['emotion_analysis'] = safe_json_dumps(message_data['emotion_analysis'])
        
        # Set timestamp
        message_data.setdefault('timestamp', get_current_timestamp())
        
        # Build query
        columns = list(message_data.keys())
        placeholders = ', '.join(['?' for _ in columns])
        query = f"""
            INSERT INTO {config.database.chat_table}
            ({', '.join(columns)})
            VALUES ({placeholders})
        """
        
        return self.execute_insert(query, tuple(message_data.values()))
    
    def save_feedback(self, feedback_data: Dict[str, Any]) -> int:
        """Save user feedback."""
        feedback_data.setdefault('timestamp', get_current_timestamp())
        
        columns = list(feedback_data.keys())
        placeholders = ', '.join(['?' for _ in columns])
        query = f"""
            INSERT INTO {config.database.feedback_table}
            ({', '.join(columns)})
            VALUES ({placeholders})
        """
        
        return self.execute_insert(query, tuple(feedback_data.values()))
    
    def health_check(self) -> bool:
        """Perform a basic health check on the database."""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return True
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return False
