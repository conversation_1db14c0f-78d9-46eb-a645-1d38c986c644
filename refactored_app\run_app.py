#!/usr/bin/env python3
"""
Simple startup script for the AI Friend application.
Handles import paths and starts the application.
"""

import sys
import os
import argparse

# Add the current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description="AI Friend Application")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind server to")
    parser.add_argument("--port", type=int, default=7861, help="Port to bind server to")
    parser.add_argument("--share", action="store_true", help="Create public Gradio link")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    parser.add_argument("--log-level", default="INFO", help="Set logging level")
    
    args = parser.parse_args()
    
    # Set environment variables from args
    os.environ["SERVER_NAME"] = args.host
    os.environ["SERVER_PORT"] = str(args.port)
    os.environ["GRADIO_SHARE"] = "true" if args.share else "false"
    os.environ["LOG_LEVEL"] = args.log_level.upper()
    
    try:
        print("🚀 Starting AI Friend Application...")
        print(f"📍 Server: http://{args.host}:{args.port}")
        print(f"🔧 Debug mode: {'ON' if args.debug else 'OFF'}")
        print(f"📊 Log level: {args.log_level.upper()}")
        print("=" * 50)
        
        # Import and run the application
        from ui.app import create_app
        
        app = create_app()
        
        # Launch the Gradio app
        app.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug,
            show_error=True,
            quiet=False
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Trying to install missing dependencies...")
        
        # Try to install basic dependencies
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "gradio", "google-generativeai", "python-dotenv", "requests", "bcrypt"])
            print("✅ Dependencies installed. Please run the script again.")
        except Exception as install_error:
            print(f"❌ Failed to install dependencies: {install_error}")
            print("\n📋 Please install manually:")
            print("pip install gradio google-generativeai python-dotenv requests bcrypt")
        
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        print("\n🔍 Debug information:")
        print(f"Python version: {sys.version}")
        print(f"Current directory: {current_dir}")
        print(f"Python path: {sys.path[:3]}...")
        
        if args.debug:
            import traceback
            traceback.print_exc()
        
        sys.exit(1)

if __name__ == "__main__":
    main()
