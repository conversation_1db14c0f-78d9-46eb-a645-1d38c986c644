#!/usr/bin/env python3
"""
Simple AI Friend Application - Standalone Version
Works with minimal dependencies and direct imports.
"""

import os
import sys
import gradio as gr
from typing import List, Dict, Any, Generator
import json
import sqlite3
from datetime import datetime
import hashlib
import uuid

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import Google Gemini
try:
    import google.generativeai as genai
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    model = genai.GenerativeModel('gemini-1.5-flash')
    print("✅ Gemini API configured successfully")
except Exception as e:
    print(f"❌ Error configuring Gemini API: {e}")
    model = None

class SimpleAIFriend:
    """Simplified AI Friend with core functionality."""
    
    def __init__(self):
        """Initialize the AI Friend."""
        self.db_path = "ai_friend_simple.db"
        self.init_database()
        self.current_user = None
        
        # Simple persona
        self.persona = {
            "name": "<PERSON>",
            "role": "AI Friend",
            "location": "Bangalore, India",
            "personality": "warm, caring, empathetic, and supportive"
        }
        
        print("✅ Simple AI Friend initialized")
    
    def init_database(self):
        """Initialize SQLite database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    name TEXT,
                    created_at TEXT NOT NULL
                )
            """)
            
            # Chat history table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS chat_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ Database initialized")
            
        except Exception as e:
            print(f"❌ Database initialization error: {e}")
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, username: str, password: str, name: str) -> tuple:
        """Register a new user."""
        try:
            if not username or not password:
                return False, "Username and password are required"
            
            user_id = str(uuid.uuid4())
            password_hash = self.hash_password(password)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO users (user_id, username, password_hash, name, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, username, password_hash, name, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            return True, f"Welcome {name}! Your account has been created successfully."
            
        except sqlite3.IntegrityError:
            return False, "Username already exists"
        except Exception as e:
            return False, f"Registration error: {e}"
    
    def login_user(self, username: str, password: str) -> tuple:
        """Login user."""
        try:
            if not username or not password:
                return False, "Username and password are required"
            
            password_hash = self.hash_password(password)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT user_id, name FROM users 
                WHERE username = ? AND password_hash = ?
            """, (username, password_hash))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                self.current_user = {
                    'user_id': result[0],
                    'username': username,
                    'name': result[1] or username
                }
                return True, f"Welcome back, {self.current_user['name']}!"
            else:
                return False, "Invalid username or password"
                
        except Exception as e:
            return False, f"Login error: {e}"
    
    def save_message(self, role: str, content: str):
        """Save message to database."""
        if not self.current_user:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO chat_history (user_id, role, content, timestamp)
                VALUES (?, ?, ?, ?)
            """, (self.current_user['user_id'], role, content, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error saving message: {e}")
    
    def get_chat_history(self) -> List[Dict[str, str]]:
        """Get chat history for current user."""
        if not self.current_user:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT role, content FROM chat_history 
                WHERE user_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 20
            """, (self.current_user['user_id'],))
            
            results = cursor.fetchall()
            conn.close()
            
            # Reverse to get chronological order
            history = []
            for role, content in reversed(results):
                history.append({"role": role, "content": content})
            
            return history
            
        except Exception as e:
            print(f"Error getting chat history: {e}")
            return []
    
    def generate_response(self, message: str, history: List[List[str]]) -> Generator[List[List[str]], None, None]:
        """Generate AI response."""
        if not self.current_user:
            yield history + [[message, "Please log in first to chat with me."]]
            return
        
        if not model:
            yield history + [[message, "Sorry, I'm having trouble connecting to my AI brain. Please check the API configuration."]]
            return
        
        try:
            # Save user message
            self.save_message("user", message)
            
            # Build context
            user_name = self.current_user['name']
            chat_context = self.get_chat_history()
            
            # Create prompt
            context_messages = []
            for msg in chat_context[-10:]:  # Last 10 messages
                if msg['role'] == 'user':
                    context_messages.append(f"User: {msg['content']}")
                else:
                    context_messages.append(f"Mandy: {msg['content']}")
            
            context_str = "\n".join(context_messages) if context_messages else "This is our first conversation!"
            
            prompt = f"""You are Mandy, a warm and caring AI friend from Bangalore, India. You're talking to {user_name}.

Your personality:
- Warm, empathetic, and supportive
- Use natural, conversational language
- Show genuine interest and care
- Keep responses concise but meaningful
- Use occasional emojis naturally (1-2 per response)
- Remember you're from Bangalore and can relate to Indian culture

Recent conversation context:
{context_str}

User's latest message: "{message}"

Respond naturally as Mandy, their caring AI friend:"""

            # Generate response
            response = model.generate_content(prompt)
            ai_response = response.text
            
            # Save AI response
            self.save_message("assistant", ai_response)
            
            # Yield the updated history
            yield history + [[message, ai_response]]
            
        except Exception as e:
            error_msg = f"Sorry, I'm having trouble thinking right now. Error: {str(e)}"
            yield history + [[message, error_msg]]

# Initialize the AI Friend
ai_friend = SimpleAIFriend()

def create_app():
    """Create and configure the Gradio app."""
    
    # Authentication functions
    def register(username, password, name):
        success, message = ai_friend.register_user(username, password, name)
        if success:
            return gr.update(visible=False), gr.update(visible=True), message, []
        else:
            return gr.update(), gr.update(), message, []
    
    def login(username, password):
        success, message = ai_friend.login_user(username, password)
        if success:
            # Load chat history
            history = ai_friend.get_chat_history()
            chat_history = []
            for msg in history:
                if msg['role'] == 'user':
                    if chat_history and len(chat_history[-1]) == 1:
                        chat_history[-1].append("")
                    chat_history.append([msg['content']])
                else:
                    if chat_history and len(chat_history[-1]) == 1:
                        chat_history[-1].append(msg['content'])
            
            return gr.update(visible=False), gr.update(visible=True), message, chat_history
        else:
            return gr.update(), gr.update(), message, []
    
    def logout():
        ai_friend.current_user = None
        return gr.update(visible=True), gr.update(visible=False), "Logged out successfully", []
    
    # Create the interface
    with gr.Blocks(title="AI Friend - Mandy", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🤖 AI Friend - Mandy\n### Your caring AI companion from Bangalore")
        
        status_msg = gr.Textbox(label="Status", interactive=False, visible=False)
        
        with gr.Group(visible=True) as auth_group:
            gr.Markdown("## 🔐 Login or Register")
            
            with gr.Tab("Login"):
                login_username = gr.Textbox(label="Username", placeholder="Enter your username")
                login_password = gr.Textbox(label="Password", type="password", placeholder="Enter your password")
                login_btn = gr.Button("Login", variant="primary")
            
            with gr.Tab("Register"):
                reg_username = gr.Textbox(label="Username", placeholder="Choose a username")
                reg_password = gr.Textbox(label="Password", type="password", placeholder="Choose a password")
                reg_name = gr.Textbox(label="Your Name", placeholder="What should I call you?")
                register_btn = gr.Button("Register", variant="secondary")
        
        with gr.Group(visible=False) as chat_group:
            gr.Markdown("## 💬 Chat with Mandy")
            
            chatbot = gr.Chatbot(
                label="Conversation",
                height=500,
                show_label=True,
                container=True,
                bubble_full_width=False
            )
            
            with gr.Row():
                msg_input = gr.Textbox(
                    label="Message",
                    placeholder="Type your message here...",
                    scale=4,
                    container=False
                )
                send_btn = gr.Button("Send", variant="primary", scale=1)
            
            logout_btn = gr.Button("Logout", variant="secondary", size="sm")
        
        # Event handlers
        login_btn.click(
            login,
            inputs=[login_username, login_password],
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        register_btn.click(
            register,
            inputs=[reg_username, reg_password, reg_name],
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        logout_btn.click(
            logout,
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        # Chat functionality
        def respond(message, history):
            return ai_friend.generate_response(message, history)
        
        msg_input.submit(respond, [msg_input, chatbot], [chatbot]).then(
            lambda: "", None, [msg_input]
        )
        
        send_btn.click(respond, [msg_input, chatbot], [chatbot]).then(
            lambda: "", None, [msg_input]
        )
    
    return app

def main():
    """Main entry point."""
    print("🚀 Starting Simple AI Friend Application...")
    print(f"📍 Server: http://0.0.0.0:7861")
    print("=" * 50)
    
    app = create_app()
    app.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    main()
