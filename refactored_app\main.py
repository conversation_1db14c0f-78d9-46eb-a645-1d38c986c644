"""
Main entry point for the AI Friend application.
Handles application startup, configuration validation, and graceful shutdown.
"""

import argparse
import signal
import sys
from pathlib import Path

# Download required NLTK data
try:
    import nltk
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
    nltk.download('vader_lexicon', quiet=True)
except ImportError:
    pass  # NLTK not available

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import config
from core import setup_logging, get_logger
# Import UI factory with graceful fallback
try:
    from ui.interface import create_full_app as create_app
except Exception:
    from simple_app import create_app


def setup_signal_handlers(app):
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger = get_logger(__name__)
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        
        try:
            app.close()
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def validate_environment():
    """Validate environment and configuration."""
    logger = get_logger(__name__)
    
    try:
        # Validate configuration
        config.validate()
        logger.info("Configuration validation passed")
        
        # Check required directories
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        return True
        
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        return False


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="AI Friend - Your Personal AI Companion")
    
    parser.add_argument(
        "--host",
        default=config.ui.server_name,
        help="Host to bind the server to (default: %(default)s)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=config.ui.server_port,
        help="Port to bind the server to (default: %(default)s)"
    )
    
    parser.add_argument(
        "--share",
        action="store_true",
        default=config.ui.share_gradio,
        help="Create a public Gradio link"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=config.log_level,
        help="Set logging level (default: %(default)s)"
    )
    
    return parser.parse_args()


def main():
    """Main application entry point."""
    # Parse command line arguments
    args = parse_arguments()
    
    # Override config with command line arguments
    if args.debug:
        config.log_level = "DEBUG"
    else:
        config.log_level = args.log_level
    
    # Setup logging
    setup_logging("ai_friend")
    logger = get_logger(__name__)
    
    logger.info("=" * 60)
    logger.info("AI Friend Application Starting")
    logger.info("=" * 60)
    
    try:
        # Validate environment
        if not validate_environment():
            logger.error("Environment validation failed, exiting")
            sys.exit(1)
        
        # Create application
        logger.info("Creating application instance...")
        app = create_app()
        
        # Setup signal handlers for graceful shutdown
        setup_signal_handlers(app)
        
        # Launch application
        logger.info("Launching AI Friend application...")
        app.launch(
            server_name=args.host,
            server_port=args.port,
            share=args.share,
            debug=args.debug
        )
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"Application failed to start: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
