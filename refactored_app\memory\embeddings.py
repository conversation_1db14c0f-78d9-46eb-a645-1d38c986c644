"""
Embedding utilities for the memory system.
Handles text embedding generation and vector operations with FAISS support.
"""

import threading
from functools import lru_cache
from typing import List, Optional

import numpy as np
import torch
# Defer importing sentence_transformers until _load_model is called to avoid heavy import at test collection
SentenceTransformer = None  # type: ignore

try:
    import faiss
except ImportError:
    faiss = None

try:
    from ..config import config
    from ..core import <PERSON><PERSON><PERSON><PERSON><PERSON>, MemoryError, normalize_embedding
except ImportError:
    from config import config
    from core import LoggerMixin, MemoryError, normalize_embedding


class EmbeddingManager(LoggerMixin):
    """Manages text embeddings for the memory system."""

    _instance = None
    _lock = threading.RLock()

    def __new__(cls):
        """Singleton pattern for embedding manager."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """Initialize the embedding manager."""
        if self._initialized:
            return

        # Defer hard dependency checks until first real use to keep import light
        self.model = None
        self.dimension = None
        self.batch_size = 32
        self._load_model()
        self._initialized = True

    def _load_model(self) -> None:
        """Load the sentence transformer model."""
        try:
            self.logger.info(f"Loading embedding model: {config.memory.embedding_model}")

            global SentenceTransformer
            if SentenceTransformer is None:
                try:
                    from sentence_transformers import SentenceTransformer as _ST
                    SentenceTransformer = _ST
                except ImportError:
                    raise MemoryError("sentence-transformers is not installed. Install with: pip install sentence-transformers")

            self.model = SentenceTransformer(config.memory.embedding_model)

            # Optimize for GPU if available
            if torch.cuda.is_available():
                self.model = self.model.to(torch.device("cuda"))
                self.logger.info("Using CUDA for embedding acceleration")

            # Get embedding dimension
            test_embedding = self.model.encode("test", normalize_embeddings=True)
            self.dimension = len(test_embedding)

            self.logger.info(f"Embedding model loaded successfully (dimension: {self.dimension})")

        except Exception as e:
            self.logger.error(f"Failed to load embedding model: {e}")
            raise MemoryError(f"Failed to load embedding model: {e}")

    @lru_cache(maxsize=8192)
    def get_embedding(self, text: str) -> Optional[np.ndarray]:
        """
        Generate normalized embedding for text.
        
        Args:
            text: Input text to embed
            
        Returns:
            Normalized embedding vector or None on error
        """
        if not isinstance(text, str) or not text.strip():
            return np.zeros(self.dimension, dtype=np.float32)
        
        try:
            # Clean text
            processed_text = " ".join(text.strip().split())
            
            # Generate embedding
            with torch.no_grad():
                embedding = self.model.encode(
                    processed_text,
                    convert_to_numpy=True,
                    normalize_embeddings=True,
                    show_progress_bar=False
                )
            
            # Ensure correct format
            embedding = embedding.astype(np.float32)
            if embedding.ndim != 1:
                embedding = embedding.flatten()
            
            # Final normalization check
            return normalize_embedding(embedding)
            
        except Exception as e:
            self.logger.error(f"Error generating embedding: {e}")
            return np.zeros(self.dimension, dtype=np.float32)
    
    def get_embeddings_batch(self, texts: List[str]) -> List[np.ndarray]:
        """
        Generate embeddings for multiple texts efficiently.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            # Filter and process texts
            valid_texts = []
            invalid_indices = []
            
            for i, text in enumerate(texts):
                if isinstance(text, str) and text.strip():
                    valid_texts.append(" ".join(text.strip().split()))
                else:
                    invalid_indices.append(i)
            
            # Generate embeddings in batches
            all_embeddings = []
            
            for i in range(0, len(valid_texts), self.batch_size):
                batch = valid_texts[i:i + self.batch_size]
                
                with torch.no_grad():
                    batch_embeddings = self.model.encode(
                        batch,
                        convert_to_numpy=True,
                        normalize_embeddings=True,
                        show_progress_bar=False,
                        batch_size=self.batch_size
                    )
                
                # Ensure correct format
                batch_embeddings = batch_embeddings.astype(np.float32)
                all_embeddings.extend(batch_embeddings)
            
            # Reconstruct result list with None for invalid texts
            result = []
            valid_idx = 0
            
            for i in range(len(texts)):
                if i in invalid_indices:
                    result.append(np.zeros(self.dimension, dtype=np.float32))
                else:
                    result.append(all_embeddings[valid_idx])
                    valid_idx += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in batch embedding generation: {e}")
            return [np.zeros(self.dimension, dtype=np.float32) for _ in texts]
    
    def cosine_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score (0-1)
        """
        try:
            # Ensure embeddings are normalized
            emb1 = normalize_embedding(embedding1)
            emb2 = normalize_embedding(embedding2)
            
            # Calculate dot product (cosine similarity for normalized vectors)
            similarity = np.dot(emb1, emb2)
            
            # Clamp to valid range
            return max(0.0, min(1.0, float(similarity)))
            
        except Exception as e:
            self.logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    def find_similar_embeddings(self, query_embedding: np.ndarray, 
                               embeddings: List[np.ndarray], 
                               threshold: float = 0.5) -> List[tuple]:
        """
        Find embeddings similar to query embedding.
        
        Args:
            query_embedding: Query embedding vector
            embeddings: List of embeddings to search
            threshold: Minimum similarity threshold
            
        Returns:
            List of (index, similarity_score) tuples
        """
        try:
            similarities = []
            
            for i, embedding in enumerate(embeddings):
                similarity = self.cosine_similarity(query_embedding, embedding)
                if similarity >= threshold:
                    similarities.append((i, similarity))
            
            # Sort by similarity (descending)
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            return similarities
            
        except Exception as e:
            self.logger.error(f"Error finding similar embeddings: {e}")
            return []
    
    def embedding_to_bytes(self, embedding: np.ndarray) -> bytes:
        """Convert embedding to bytes for database storage."""
        try:
            normalized = normalize_embedding(embedding)
            return normalized.tobytes()
        except Exception as e:
            self.logger.error(f"Error converting embedding to bytes: {e}")
            return np.zeros(self.dimension, dtype=np.float32).tobytes()
    
    def bytes_to_embedding(self, data: bytes) -> np.ndarray:
        """Convert bytes back to embedding array."""
        try:
            embedding = np.frombuffer(data, dtype=np.float32)
            if len(embedding) == self.dimension:
                return embedding
            else:
                self.logger.warning(f"Invalid embedding dimension: {len(embedding)}, expected {self.dimension}")
                return np.zeros(self.dimension, dtype=np.float32)
        except Exception as e:
            self.logger.error(f"Error converting bytes to embedding: {e}")
            return np.zeros(self.dimension, dtype=np.float32)
    
    @property
    def embedding_dimension(self) -> int:
        """Get the embedding dimension."""
        return self.dimension


# Lazy global accessor to avoid heavy init at import time
_embedding_singleton = None

def get_embedding_manager() -> EmbeddingManager:
    global _embedding_singleton
    if _embedding_singleton is None:
        _embedding_singleton = EmbeddingManager()
    return _embedding_singleton
