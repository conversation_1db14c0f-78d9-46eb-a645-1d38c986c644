"""
AI Friend Application - Refactored Version

A clean, maintainable AI chatbot application with proper architecture,
separation of concerns, and production-ready code structure.

Main Components:
- config: Configuration management
- core: Core utilities and exceptions
- database: Database management and models
- auth: Authentication and user management
- memory: Memory system with embeddings
- llm: Language model client
- chat: Chat management and conversation flow
- ui: Gradio user interface

Usage:
    from refactored_app import create_app
    
    app = create_app()
    app.launch()
"""

# Avoid importing heavy UI/features at package import time.
# Consumers can import UI via `from refactored_app.ui import create_app` if needed.

__version__ = "2.0.0"
__author__ = "AI Friend Development Team"

__all__ = []
