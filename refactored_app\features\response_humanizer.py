"""
Response Humanizer for natural language processing.
Makes AI responses more human-like and natural.
"""

import random
import re
from typing import Dict, Any, List, Optional

try:
    from ..config import config
    from ..core import LoggerMixin
except Exception:
    from config import config
    from core import LoggerMixin


class ResponseHumanizer(LoggerMixin):
    """
    Humanizes AI responses to make them more natural and conversational.
    Adds human-like imperfections, variations, and natural speech patterns.
    """
    
    def __init__(self):
        """Initialize response humanizer."""
        # Conversation fillers and natural speech patterns
        self.conversation_fillers = [
            "you know", "I mean", "like", "actually", "honestly", "basically",
            "sort of", "kind of", "I guess", "well", "um", "oh"
        ]
        
        # Casual contractions
        self.contractions = {
            "I am": "I'm",
            "you are": "you're", 
            "we are": "we're",
            "they are": "they're",
            "it is": "it's",
            "that is": "that's",
            "there is": "there's",
            "I have": "I've",
            "you have": "you've",
            "we have": "we've",
            "they have": "they've",
            "I will": "I'll",
            "you will": "you'll",
            "we will": "we'll",
            "they will": "they'll",
            "would not": "wouldn't",
            "could not": "couldn't",
            "should not": "shouldn't",
            "do not": "don't",
            "does not": "doesn't",
            "did not": "didn't",
            "cannot": "can't",
            "will not": "won't",
            "is not": "isn't",
            "are not": "aren't",
            "was not": "wasn't",
            "were not": "weren't"
        }
        
        # Sentence starters for variety
        self.sentence_starters = [
            "Oh,", "Well,", "Actually,", "Honestly,", "You know,", "I mean,",
            "So,", "Anyway,", "By the way,", "Speaking of which,"
        ]
        
        # Enthusiasm markers
        self.enthusiasm_markers = [
            "!", "!!", "✨", "😊", "🎉", "💫", "🌟"
        ]
        
        # Thinking expressions
        self.thinking_expressions = [
            "Hmm,", "Let me think...", "Well,", "I'm thinking...", "You know what,"
        ]
        
        # Agreement expressions
        self.agreement_expressions = [
            "Absolutely!", "Totally!", "Exactly!", "For sure!", "Definitely!",
            "I completely agree!", "You're so right!", "That's so true!"
        ]
        
        # Empathy expressions
        self.empathy_expressions = [
            "I can understand that", "That makes sense", "I hear you",
            "That sounds tough", "I can imagine", "That must be"
        ]
        
        self.logger.info("Response humanizer initialized")
    
    def humanize_response(self, response: str, style_params: Dict[str, Any]) -> str:
        """
        Humanize an AI response based on style parameters.
        
        Args:
            response: Original AI response
            style_params: Style parameters including warmth, humor, formality levels
            
        Returns:
            Humanized response
        """
        try:
            if not response or not response.strip():
                return response
            
            humanized = response
            
            # Apply humanization techniques based on style parameters
            warmth_level = style_params.get('warmth_level', 0.7)
            humor_level = style_params.get('humor_level', 5)
            formality_level = style_params.get('formality_level', 0.3)
            use_emojis = style_params.get('use_emojis', True)
            use_slang = style_params.get('use_slang', False)
            
            # 1. Apply contractions for casual tone
            if formality_level < 0.6:
                humanized = self._apply_contractions(humanized)
            
            # 2. Add conversation fillers occasionally
            if formality_level < 0.4 and random.random() < 0.3:
                humanized = self._add_conversation_fillers(humanized)
            
            # 3. Vary sentence starters
            if random.random() < 0.2:
                humanized = self._vary_sentence_starters(humanized)
            
            # 4. Add enthusiasm based on warmth
            if warmth_level > 0.7 and random.random() < warmth_level:
                humanized = self._add_enthusiasm(humanized, use_emojis)
            
            # 5. Add thinking expressions for complex responses
            if len(response) > 100 and random.random() < 0.15:
                humanized = self._add_thinking_expressions(humanized)
            
            # 6. Apply casual language patterns
            if use_slang and formality_level < 0.4:
                humanized = self._apply_casual_language(humanized)
            
            # 7. Add natural imperfections occasionally
            if formality_level < 0.5 and random.random() < 0.1:
                humanized = self._add_natural_imperfections(humanized)
            
            # 8. Adjust punctuation for casualness
            if formality_level < 0.5:
                humanized = self._adjust_punctuation(humanized)
            
            return humanized.strip()
            
        except Exception as e:
            self.logger.error(f"Error humanizing response: {e}")
            return response
    
    def _apply_contractions(self, text: str) -> str:
        """Apply contractions to make text more casual."""
        for full_form, contraction in self.contractions.items():
            # Case-insensitive replacement
            pattern = re.compile(re.escape(full_form), re.IGNORECASE)
            text = pattern.sub(contraction, text)
        
        return text
    
    def _add_conversation_fillers(self, text: str) -> str:
        """Add natural conversation fillers."""
        sentences = text.split('. ')
        
        if len(sentences) > 1 and random.random() < 0.4:
            # Add filler to a random sentence
            filler_index = random.randint(0, len(sentences) - 1)
            filler = random.choice(self.conversation_fillers)
            
            sentence = sentences[filler_index]
            # Insert filler at beginning or after first few words
            if random.random() < 0.5:
                sentences[filler_index] = f"{filler}, {sentence}"
            else:
                words = sentence.split()
                if len(words) > 3:
                    insert_pos = random.randint(2, min(4, len(words)))
                    words.insert(insert_pos, f"{filler},")
                    sentences[filler_index] = ' '.join(words)
        
        return '. '.join(sentences)
    
    def _vary_sentence_starters(self, text: str) -> str:
        """Add variety to sentence starters."""
        if random.random() < 0.3:
            starter = random.choice(self.sentence_starters)
            if not text.startswith(tuple(self.sentence_starters)):
                text = f"{starter} {text.lower()}"
        
        return text
    
    def _add_enthusiasm(self, text: str, use_emojis: bool) -> str:
        """Add enthusiasm markers to text."""
        if use_emojis and random.random() < 0.6:
            emoji = random.choice(self.enthusiasm_markers)
            
            # Add emoji at end or replace existing punctuation
            if text.endswith('.'):
                text = text[:-1] + f" {emoji}"
            elif not text.endswith(('!', '?', '✨', '😊', '🎉', '💫', '🌟')):
                text = f"{text} {emoji}"
        
        # Occasionally add exclamation for enthusiasm
        if random.random() < 0.3 and text.endswith('.'):
            text = text[:-1] + '!'
        
        return text
    
    def _add_thinking_expressions(self, text: str) -> str:
        """Add thinking expressions for complex responses."""
        thinking = random.choice(self.thinking_expressions)
        return f"{thinking} {text}"
    
    def _apply_casual_language(self, text: str) -> str:
        """Apply casual language patterns."""
        casual_replacements = {
            "very good": "really good",
            "very nice": "really nice", 
            "very interesting": "super interesting",
            "extremely": "really",
            "certainly": "definitely",
            "perhaps": "maybe",
            "indeed": "totally",
            "quite": "pretty",
            "rather": "kinda",
            "somewhat": "sort of"
        }
        
        for formal, casual in casual_replacements.items():
            text = re.sub(r'\b' + re.escape(formal) + r'\b', casual, text, flags=re.IGNORECASE)
        
        return text
    
    def _add_natural_imperfections(self, text: str) -> str:
        """Add subtle natural imperfections."""
        # Occasionally start with "Oh" or "Well"
        if random.random() < 0.5:
            starters = ["Oh,", "Well,", "Hmm,"]
            starter = random.choice(starters)
            text = f"{starter} {text.lower()}"
        
        # Occasionally add self-correction
        if random.random() < 0.3 and len(text) > 50:
            corrections = [
                "I mean,", "or rather,", "well, actually,", "wait, let me rephrase that -"
            ]
            correction = random.choice(corrections)
            
            # Insert correction in middle of text
            sentences = text.split('. ')
            if len(sentences) > 1:
                insert_pos = len(sentences) // 2
                sentences.insert(insert_pos, correction)
                text = '. '.join(sentences)
        
        return text
    
    def _adjust_punctuation(self, text: str) -> str:
        """Adjust punctuation for casual tone."""
        # Occasionally use multiple punctuation marks
        if random.random() < 0.2:
            if text.endswith('!'):
                text = text[:-1] + '!!'
            elif text.endswith('?'):
                text = text[:-1] + '??'
        
        # Occasionally remove periods from short sentences
        if random.random() < 0.3 and len(text) < 30 and text.endswith('.'):
            text = text[:-1]
        
        return text
    
    def add_empathy_response(self, text: str, emotion: str) -> str:
        """Add empathetic response based on detected emotion."""
        try:
            if emotion in ['sadness', 'fear', 'anger', 'anxiety']:
                empathy = random.choice(self.empathy_expressions)
                return f"{empathy}. {text}"
            elif emotion in ['joy', 'excitement', 'happiness']:
                agreement = random.choice(self.agreement_expressions)
                return f"{agreement} {text}"
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error adding empathy response: {e}")
            return text
    
    def adjust_for_relationship_stage(self, text: str, stage: str) -> str:
        """Adjust response based on relationship stage."""
        try:
            if stage == "acquaintance":
                # More formal, less casual
                text = re.sub(r'\b(totally|super|really)\b', 'quite', text, flags=re.IGNORECASE)
                text = re.sub(r'!!+', '!', text)
                
            elif stage == "close friend":
                # More casual, add more personality
                if random.random() < 0.4:
                    casual_additions = [
                        "haha", "lol", "omg", "tbh", "ngl"
                    ]
                    addition = random.choice(casual_additions)
                    text = f"{text} {addition}"
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error adjusting for relationship stage: {e}")
            return text
    
    def add_regional_flavor(self, text: str, region: str = "India") -> str:
        """Add regional language flavor."""
        try:
            if region.lower() == "india":
                # Add occasional Indian English expressions
                indian_expressions = {
                    "very good": "very good yaar",
                    "really nice": "really nice na",
                    "that's great": "that's great yaar",
                    "awesome": "awesome re",
                    "cool": "cool na"
                }
                
                if random.random() < 0.2:  # 20% chance
                    for english, indian in indian_expressions.items():
                        if english in text.lower():
                            text = re.sub(re.escape(english), indian, text, flags=re.IGNORECASE)
                            break
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error adding regional flavor: {e}")
            return text
