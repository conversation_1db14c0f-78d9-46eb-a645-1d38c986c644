"""
Full Gradio UI for AI Friend application with all advanced features.
"""

import os
import sys
import gradio as gr
from typing import List, Dict, Any, Generator
import json
import sqlite3
from datetime import datetime
import hashlib
import uuid

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Import Google Gemini
try:
    import google.generativeai as genai
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    model = genai.GenerativeModel('gemini-1.5-flash')
    print("✅ Gemini API configured successfully")
except Exception as e:
    print(f"❌ Error configuring Gemini API: {e}")
    model = None

# Try to import advanced features
try:
    # Add parent directory to path for imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    
    # Import advanced features
    from features.emotion_analyzer import AdvancedEmotionAnalyzer
    from features.interest_tracker import IntelligentInterestTracker
    from features.personality_engine import PersonalityEngine
    from features.relationship_manager import RelationshipManager
    from features.time_awareness import TimeAwarenessManager
    from features.festival_tracker import FestivalTracker
    from features.response_humanizer import ResponseHumanizer
    from features.news_fetcher import NewsFetcher
    from features.rate_limiter import conversation_rate_limiter
    from memory.faiss_manager import FAISSMemoryManager
    from database.manager import DatabaseManager
    
    ADVANCED_FEATURES_AVAILABLE = True
    print("✅ Advanced features imported successfully")
    
except ImportError as e:
    print(f"⚠️ Advanced features not available: {e}")
    ADVANCED_FEATURES_AVAILABLE = False

class FullAIFriend:
    """Full AI Friend with all advanced features."""
    
    def __init__(self):
        """Initialize the Full AI Friend."""
        self.db_path = "ai_friend_full.db"
        self.init_database()
        self.current_user = None
        
        # Initialize advanced features if available
        if ADVANCED_FEATURES_AVAILABLE:
            try:
                self.db_manager = DatabaseManager(self.db_path)
                self.emotion_analyzer = AdvancedEmotionAnalyzer()
                self.interest_tracker = IntelligentInterestTracker(self.db_manager)
                self.memory_manager = FAISSMemoryManager(self.db_manager)
                self.festival_tracker = FestivalTracker()
                self.response_humanizer = ResponseHumanizer()
                self.news_fetcher = NewsFetcher()
                self.time_awareness = TimeAwarenessManager(self.db_manager)
                
                # Create simple persona config for personality engine
                persona_config = {
                    'adaptability': {
                        'warmth': 0.7,
                        'humor': 0.5,
                        'empathy': 0.8,
                        'curiosity': 0.6,
                        'formality': 0.3
                    }
                }
                self.personality_engine = PersonalityEngine(persona_config, self.db_manager)
                self.relationship_manager = RelationshipManager(self.db_manager)
                
                print("✅ All advanced features initialized")
                self.features_enabled = True
                
            except Exception as e:
                print(f"⚠️ Error initializing advanced features: {e}")
                self.features_enabled = False
        else:
            self.features_enabled = False
        
        # Simple persona
        self.persona = {
            "name": "Mandy",
            "role": "AI Friend",
            "location": "Bangalore, India",
            "personality": "warm, caring, empathetic, and supportive"
        }
        
        print(f"✅ Full AI Friend initialized (Advanced features: {'ON' if self.features_enabled else 'OFF'})")
    
    def init_database(self):
        """Initialize SQLite database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Users table with advanced fields
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    name TEXT,
                    created_at TEXT NOT NULL,
                    last_interaction_time TEXT,
                    relationship_depth REAL DEFAULT 0.0,
                    personality_traits TEXT
                )
            """)
            
            # Chat history table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS chat_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    emotion_analysis TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            """)
            
            # Session tracking table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    session_id TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    message_count INTEGER DEFAULT 0,
                    duration_seconds INTEGER DEFAULT 0,
                    FOREIGN KEY (user_id) REFERENCES users(user_id)
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ Full database initialized")
            
        except Exception as e:
            print(f"❌ Database initialization error: {e}")
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA-256."""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, username: str, password: str, name: str) -> tuple:
        """Register a new user."""
        try:
            if not username or not password:
                return False, "Username and password are required"
            
            user_id = str(uuid.uuid4())
            password_hash = self.hash_password(password)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO users (user_id, username, password_hash, name, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, username, password_hash, name, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            return True, f"Welcome {name}! Your account has been created successfully."
            
        except sqlite3.IntegrityError:
            return False, "Username already exists"
        except Exception as e:
            return False, f"Registration error: {e}"
    
    def login_user(self, username: str, password: str) -> tuple:
        """Login user."""
        try:
            if not username or not password:
                return False, "Username and password are required"
            
            password_hash = self.hash_password(password)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT user_id, name FROM users 
                WHERE username = ? AND password_hash = ?
            """, (username, password_hash))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                self.current_user = {
                    'user_id': result[0],
                    'username': username,
                    'name': result[1] or username
                }
                
                # Start session if advanced features are available
                if self.features_enabled:
                    try:
                        self.time_awareness.start_session(self.current_user['user_id'])
                    except:
                        pass
                
                return True, f"Welcome back, {self.current_user['name']}!"
            else:
                return False, "Invalid username or password"
                
        except Exception as e:
            return False, f"Login error: {e}"
    
    def save_message(self, role: str, content: str, emotion_analysis: str = None):
        """Save message to database."""
        if not self.current_user:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO chat_history (user_id, role, content, timestamp, emotion_analysis)
                VALUES (?, ?, ?, ?, ?)
            """, (self.current_user['user_id'], role, content, datetime.now().isoformat(), emotion_analysis))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error saving message: {e}")
    
    def get_chat_history(self) -> List[Dict[str, str]]:
        """Get chat history for current user."""
        if not self.current_user:
            return []
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT role, content FROM chat_history 
                WHERE user_id = ? 
                ORDER BY timestamp DESC 
                LIMIT 20
            """, (self.current_user['user_id'],))
            
            results = cursor.fetchall()
            conn.close()
            
            # Reverse to get chronological order
            history = []
            for role, content in reversed(results):
                history.append({"role": role, "content": content})
            
            return history
            
        except Exception as e:
            print(f"Error getting chat history: {e}")
            return []

        def _normalize_pairs(self, history: List[Any]) -> List[List[str]]:
            """Normalize incoming history to a list of [user, assistant] string pairs as lists."""
            pairs: List[List[str]] = []
            if not history:
                return pairs
            for item in history:
                try:
                    a, b = item  # unpack
                except Exception:
                    # Handle singletons or scalars
                    if isinstance(item, (list, tuple)) and len(item) == 1:
                        a, b = item[0], ""
                    else:
                        a, b = str(item), ""
                pairs.append([str(a) if a is not None else "", str(b) if b is not None else ""])
            return pairs

        def generate_response(self, message: str, history: List[List[str]]) -> Generator[List[List[str]], None, None]:
            """Generate AI response with advanced features."""
        if not self.current_user:
            pairs = self._normalize_pairs(history)
            yield pairs + [[str(message), "Please log in first to chat with me."]]
            return

        if not model:
            pairs = self._normalize_pairs(history)
            yield pairs + [[str(message), "Sorry, I'm having trouble connecting to my AI brain. Please check the API configuration."]]
            return

        try:
            # Advanced processing if features are enabled
            if self.features_enabled:
                try:
                    # Rate limiting check
                    conversation_rate_limiter.check_message_rate_limit(self.current_user['user_id'], message)
                    
                    # Emotion analysis
                    emotion_analysis = self.emotion_analyzer.analyze_comprehensive(message)
                    
                    # Interest tracking
                    interests = self.interest_tracker.extract_interests_from_text(message, self.current_user['user_id'])
                    
                    # Update relationship
                    interaction_data = {
                        'user_emotion': emotion_analysis.get('emotion', {}).get('primary_emotion', 'neutral'),
                        'vulnerability_score': 0.5,  # Simplified calculation
                        'message_length': len(message),
                        'reciprocity_signal': any(word in message.lower() for word in ['you', 'your']),
                        'emotional_intensity': emotion_analysis.get('emotion', {}).get('intensity', 0.5)
                    }
                    
                    self.relationship_manager.update_relationship_depth(self.current_user['user_id'], interaction_data)
                    self.personality_engine.adapt_personality(self.current_user['user_id'], interaction_data)
                    
                    # Get relationship stage
                    relationship_stage, relationship_depth = self.relationship_manager.get_relationship_stage_and_depth(self.current_user['user_id'])
                    
                    # Get festival context
                    festival_context = self.festival_tracker.get_festival_context()
                    
                    # Save user message with emotion analysis
                    self.save_message("user", message, json.dumps(emotion_analysis))
                    
                except Exception as e:
                    print(f"Advanced features error: {e}")
                    # Fall back to simple processing
                    emotion_analysis = {'emotion': {'primary_emotion': 'neutral'}}
                    relationship_stage = 'friend'
                    festival_context = {}
            else:
                # Simple processing
                emotion_analysis = {'emotion': {'primary_emotion': 'neutral'}}
                relationship_stage = 'friend'
                festival_context = {}
                self.save_message("user", message)
            
            # Build context
            user_name = self.current_user['name']
            chat_context = self.get_chat_history()
            
            # Create enhanced prompt
            context_messages = []
            for msg in chat_context[-10:]:  # Last 10 messages
                if msg['role'] == 'user':
                    context_messages.append(f"User: {msg['content']}")
                else:
                    context_messages.append(f"Mandy: {msg['content']}")
            
            context_str = "\n".join(context_messages) if context_messages else "This is our first conversation!"
            
            # Add festival greeting if applicable
            festival_greeting = ""
            if festival_context.get('has_festival_today'):
                festival_greeting = f"\n\nFestival context: {festival_context.get('festival_greeting', '')}"
            
            prompt = f"""You are Mandy, a warm and caring AI friend from Bangalore, India. You're talking to {user_name}.

Your personality:
- Warm, empathetic, and supportive
- Use natural, conversational language
- Show genuine interest and care
- Keep responses concise but meaningful
- Use occasional emojis naturally (1-2 per response)
- Remember you're from Bangalore and can relate to Indian culture

Relationship stage: {relationship_stage}
User's emotional state: {emotion_analysis.get('emotion', {}).get('primary_emotion', 'neutral')}

Recent conversation context:
{context_str}

{festival_greeting}

User's latest message: "{message}"

Respond naturally as Mandy, their caring AI friend:"""

            # Generate response
            response = model.generate_content(prompt)
            ai_response = response.text
            
            # Apply humanization if available
            if self.features_enabled:
                try:
                    style_params = self.personality_engine.get_style_parameters(self.current_user['user_id'], relationship_stage)
                    ai_response = self.response_humanizer.humanize_response(ai_response, style_params)
                    ai_response = self.response_humanizer.adjust_for_relationship_stage(ai_response, relationship_stage)
                    ai_response = self.response_humanizer.add_regional_flavor(ai_response)
                except:
                    pass  # Fall back to original response
            
            # Save AI response
            self.save_message("assistant", ai_response)
            
            # Update session activity
            if self.features_enabled:
                try:
                    self.time_awareness.update_session_activity(self.current_user['user_id'])
                except:
                    pass
            
            # Yield the updated history
            pairs = self._normalize_pairs(history)
            yield pairs + [[str(message), str(ai_response)]]

        except Exception as e:
            error_msg = f"Sorry, I'm having trouble thinking right now. Error: {str(e)}"
            pairs = self._normalize_pairs(history)
            yield pairs + [[str(message), error_msg]]

# Initialize the Full AI Friend
ai_friend = FullAIFriend()

def create_full_app():
    """Create and configure the full Gradio app."""
    
    # Authentication functions
    def register(username, password, name):
        success, message = ai_friend.register_user(username, password, name)
        if success:
            return gr.update(visible=False), gr.update(visible=True), message, []
        else:
            return gr.update(), gr.update(), message, []
    
    def login(username, password):
        success, message = ai_friend.login_user(username, password)
        if success:
            # Load chat history and normalize to list of [user, assistant] pairs (for Chatbot type='tuples')
            history = ai_friend.get_chat_history()
            chat_pairs = []
            pending_user = None
            for msg in history:
                role = msg.get('role')
                content = msg.get('content', '')
                if role == 'user':
                    if pending_user is not None:
                        # previous user had no assistant reply; close it with empty
                        chat_pairs.append([pending_user, ""])
                    pending_user = content
                else:  # assistant
                    if pending_user is None:
                        # assistant without prior user; pair with empty user message
                        chat_pairs.append(["", content])
                    else:
                        chat_pairs.append([pending_user, content])
                        pending_user = None
            if pending_user is not None:
                chat_pairs.append([pending_user, ""])  # finalize last dangling user message

            return gr.update(visible=False), gr.update(visible=True), message, chat_pairs
        else:
            return gr.update(), gr.update(), message, []

    def logout():
        if ai_friend.current_user and ai_friend.features_enabled:
            try:
                ai_friend.time_awareness.end_session(ai_friend.current_user['user_id'])
            except:
                pass
        
        ai_friend.current_user = None
        return gr.update(visible=True), gr.update(visible=False), "Logged out successfully", []
    
    # Create the interface
    feature_status = "🚀 FULL VERSION" if ai_friend.features_enabled else "⚡ BASIC VERSION"
    
    with gr.Blocks(title="AI Friend - Mandy (Full)", theme=gr.themes.Soft()) as app:
        gr.Markdown(f"# 🤖 AI Friend - Mandy {feature_status}\n### Your caring AI companion from Bangalore with advanced features")
        
        if ai_friend.features_enabled:
            gr.Markdown("""
            **🧠 Advanced Features Active:**
            - FAISS Memory System • Emotion Analysis • Interest Tracking
            - Personality Engine • Relationship Manager • Time Awareness
            - Festival Tracker • Response Humanizer • News Integration
            """)
        else:
            gr.Markdown("**⚠️ Running in basic mode - some advanced features unavailable**")
        
        status_msg = gr.Textbox(label="Status", interactive=False, visible=False)
        
        with gr.Group(visible=True) as auth_group:
            gr.Markdown("## 🔐 Login or Register")
            
            with gr.Tab("Login"):
                login_username = gr.Textbox(label="Username", placeholder="Enter your username")
                login_password = gr.Textbox(label="Password", type="password", placeholder="Enter your password")
                login_btn = gr.Button("Login", variant="primary")
            
            with gr.Tab("Register"):
                reg_username = gr.Textbox(label="Username", placeholder="Choose a username")
                reg_password = gr.Textbox(label="Password", type="password", placeholder="Choose a password")
                reg_name = gr.Textbox(label="Your Name", placeholder="What should I call you?")
                register_btn = gr.Button("Register", variant="secondary")
        
        with gr.Group(visible=False) as chat_group:
            gr.Markdown("## 💬 Chat with Mandy")
            
            chatbot = gr.Chatbot(
                label="Conversation",
                height=500,
                show_label=True,
                container=True,
                type="tuples"
            )
            
            with gr.Row():
                msg_input = gr.Textbox(
                    label="Message",
                    placeholder="Type your message here...",
                    scale=4,
                    container=False
                )
                send_btn = gr.Button("Send", variant="primary", scale=1)
            
            logout_btn = gr.Button("Logout", variant="secondary", size="sm")
        
        # Event handlers
        login_btn.click(
            login,
            inputs=[login_username, login_password],
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        register_btn.click(
            register,
            inputs=[reg_username, reg_password, reg_name],
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        logout_btn.click(
            logout,
            outputs=[auth_group, chat_group, status_msg, chatbot]
        )
        
        # Chat functionality
        def respond(message, history):
            return ai_friend.generate_response(message, history)
        
        msg_input.submit(respond, [msg_input, chatbot], [chatbot]).then(
            lambda: "", None, [msg_input]
        )
        
        send_btn.click(respond, [msg_input, chatbot], [chatbot]).then(
            lambda: "", None, [msg_input]
        )
    
    return app
