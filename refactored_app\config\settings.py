"""
Configuration management for AI Friend application.
Centralized configuration with validation and environment variable support.
"""

import os
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    name: str = "ai_friend_v4.db"
    user_table: str = "user_profile_v4"
    chat_table: str = "chat_log_v4"
    feedback_table: str = "feedback_v4"
    memory_table: str = "contextual_memory_v4"
    connection_timeout: int = 15
    busy_timeout: int = 7500
    cache_size: int = -20000  # ~20MB cache


@dataclass
class LLMConfig:
    """Large Language Model configuration."""
    api_key: str = field(default_factory=lambda: os.getenv("GEMINI_API_KEY", ""))
    default_model: str = "gemini-1.5-flash"
    summarization_model: str = "gemini-1.5-flash"
    max_history_length: int = 15
    response_delay_base: float = 0.025
    
    def __post_init__(self):
        # Do not raise here; allow tests/basic mode without LLM.
        # Validation should occur at runtime when LLM features are used.
        if not self.api_key:
            pass


@dataclass
class MemoryConfig:
    """Memory system configuration."""
    embedding_model: str = "all-MiniLM-L6-v2"
    cosine_similarity_threshold: float = 0.35
    relevance_threshold: float = 0.10
    detail_similarity_threshold: float = 0.25
    vulnerability_boost: float = 0.20
    insight_boost: float = 0.15
    feedback_boost: float = 0.30
    personal_detail_boost: float = 0.35
    factual_boost: float = 0.25
    preference_boost: float = 0.30
    long_term_promotion_threshold: float = 0.6
    long_term_boost: float = 0.20
    max_short_term_memories: int = 200
    max_micro_memories: int = 500


@dataclass
class EmotionConfig:
    """Emotion analysis configuration."""
    model_name: str = "joeddav/distilbert-base-uncased-go-emotions-student"
    sentiment_model: str = "cardiffnlp/twitter-roberta-base-sentiment-latest"
    empathy_statements: Dict[str, List[str]] = field(default_factory=lambda: {
        "acknowledge": ["I hear you.", "Okay, I understand.", "Got it.", "That makes sense."],
        "validate": ["That sounds really tough.", "It's completely okay to feel that way.", 
                    "Anyone would find that difficult.", "Your feelings are valid."],
        "support": ["I'm here for you.", "You don't have to go through this alone.", 
                   "Sending you support.", "Lean on me if you need to."],
        "care": ["I'm thinking of you.", "Hope things get better soon.", 
                "Please take care of yourself.", "It sounds like you're hurting."],
        "celebrate": ["That's fantastic news!", "Wow, congratulations!", 
                     "I'm so happy for you!", "That's amazing!"],
        "share_joy": ["That sounds wonderful!", "So great to hear!", 
                     "That made my day too!", "Love that for you!"]
    })


@dataclass
class PersonaConfig:
    """Bot persona configuration."""
    name: str = "Mandy"
    location: str = "Bangalore, India"
    company: str = "Mindely"
    role: str = "CEO"
    passion: str = "youth mental health"
    values: List[str] = field(default_factory=lambda: [
        "friendship", "caring", "empathy", "understanding", "authenticity"
    ])
    quirks: List[str] = field(default_factory=lambda: [
        "loves filter coffee", "terrible at remembering celebrity names",
        "gets easily excited about new ideas", "sometimes mixes up Kannada and English words"
    ])
    anecdotes: List[str] = field(default_factory=lambda: [
        "You won't believe it, I presented the wrong slide deck this morning... totally classic Mandy moment!",
        "Stuck in Silk Board junction traffic again... Bangalore life, right? Needed extra coffee after that.",
        "Just discovered this amazing little cafe in Koramangala, their chai is magic!",
        "Had a really inspiring chat with a young person Mindely is helping today. Makes all the stress worth it.",
        "Tried to cook biryani last night... let's just say Swiggy saved the day. 😅",
        "Feeling pretty good about a new feature we're planning for the Mindely app!",
        "The weather in Bangalore today is just perfect for a walk, trying to squeeze one in later."
    ])
    adaptability: Dict[str, float] = field(default_factory=lambda: {
        'warmth': 0.7, 'humor': 0.5, 'curiosity': 0.6, 'empathy': 0.8, 'formality': 0.3
    })


@dataclass
class SecurityConfig:
    """Security and validation configuration."""
    max_input_length: int = 10000
    rate_limit_requests: int = 5
    rate_limit_window: int = 60
    rate_limit_burst: int = 10
    password_min_length: int = 8
    username_min_length: int = 3
    username_max_length: int = 30
    session_ttl_seconds: int = field(default_factory=lambda: int(os.getenv("SESSION_TTL_SECONDS", "86400")))
    secret_key: str = field(default_factory=lambda: os.getenv("SECRET_KEY", "a-very-insecure-default-secret-key"))

    def __post_init__(self):
        if self.secret_key == "a-very-insecure-default-secret-key":
            print("WARNING: Using default secret key. Please set a SECRET_KEY environment variable for production.")


@dataclass
class PersonalityConfig:
    """Personality engine configuration."""
    adaptation_rate: float = 0.05
    decay_rate: float = 0.01
    max_adaptation: float = 0.3


@dataclass
class RelationshipConfig:
    """Relationship management configuration."""
    vulnerability_factor: float = 2.0
    interaction_factor: float = 0.1
    reciprocity_factor: float = 1.0
    frequency_factor: float = 0.5
    consistency_factor: float = 0.3


@dataclass
class UIConfig:
    """User interface configuration."""
    server_name: str = field(default_factory=lambda: os.getenv("SERVER_NAME", "0.0.0.0"))
    server_port: int = field(default_factory=lambda: int(os.getenv("SERVER_PORT", "7861")))
    share_gradio: bool = field(default_factory=lambda: os.getenv("GRADIO_SHARE", "false").lower() == 'true')
    queue_max_size: int = 20
    theme_primary_hue: str = "pink"
    theme_secondary_hue: str = "neutral"


@dataclass
class AppConfig:
    """Main application configuration."""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    memory: MemoryConfig = field(default_factory=MemoryConfig)
    emotion: EmotionConfig = field(default_factory=EmotionConfig)
    persona: PersonaConfig = field(default_factory=PersonaConfig)
    personality: PersonalityConfig = field(default_factory=PersonalityConfig)
    relationship: RelationshipConfig = field(default_factory=RelationshipConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    
    # Logging configuration
    log_level: str = "INFO"
    log_file_max_bytes: int = 10 * 1024 * 1024  # 10MB
    log_backup_count: int = 5
    
    def validate(self) -> None:
        """Validate configuration settings."""
        # Enforce secret key safety
        if self.security.secret_key == "a-very-insecure-default-secret-key":
            raise ValueError("SECRET_KEY is not set. Set a strong SECRET_KEY in the environment for production.")

        # Only enforce LLM key if LLM is enabled
        enable_llm = os.getenv("ENABLE_LLM", "true").lower() == "true"
        if enable_llm and not self.llm.api_key:
            raise ValueError("GEMINI_API_KEY is required when ENABLE_LLM=true")

        if self.memory.cosine_similarity_threshold < 0 or self.memory.cosine_similarity_threshold > 1:
            raise ValueError("cosine_similarity_threshold must be between 0 and 1")

        if self.security.password_min_length < 6:
            raise ValueError("password_min_length must be at least 6")


# Global configuration instance
config = AppConfig()
