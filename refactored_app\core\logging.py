"""
Centralized logging configuration for the AI Friend application.
Provides structured logging with proper formatting and rotation.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

try:
    from ..config import config
except ImportError:
    from config import config


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s | %(levelname)-8s | %(process)d | %(thread)d | '
                '%(filename)s:%(lineno)d | %(funcName)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )


def setup_logging(log_file_prefix: str = "ai_friend") -> None:
    """
    Configure application logging with structured format and rotation.
    
    Args:
        log_file_prefix: Prefix for log file names
    """
    # Remove existing handlers to avoid duplicates
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # Ensure logs directory exists
    Path("logs").mkdir(parents=True, exist_ok=True)

    # Create formatter
    formatter = StructuredFormatter()

    # Setup file handler with rotation
    log_file = f"logs/{log_file_prefix}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file,
        maxBytes=config.log_file_max_bytes,
        backupCount=config.log_backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # Setup error file handler
    error_file = f"logs/{log_file_prefix}_error.log"
    error_handler = logging.handlers.RotatingFileHandler(
        filename=error_file,
        maxBytes=config.log_file_max_bytes // 2,
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(
        '%(levelname)-8s | %(filename)s:%(lineno)d | %(message)s'
    ))
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, config.log_level.upper()))
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)
    logger.addHandler(console_handler)
    
    # Reduce noise from third-party libraries
    for lib in ["httpx", "httpcore", "requests", "urllib3", "PIL", "torch", "transformers"]:
        logging.getLogger(lib).setLevel(logging.WARNING)
    
    # Keep important logs
    logging.getLogger("google.generativeai").setLevel(logging.INFO)
    
    logging.info("Structured logging configured")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for a specific module."""
    return logging.getLogger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        return get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")


def log_event(event_type: str, **kwargs) -> None:
    """Log a structured event."""
    import json
    event_data = {"event_type": event_type, **kwargs}
    logging.info(f"EVENT: {json.dumps(event_data)}")


def log_metric(metric_name: str, value: float, **kwargs) -> None:
    """Log a metric with consistent format."""
    import json
    metric_data = {"metric": metric_name, "value": value, **kwargs}
    logging.info(f"METRIC: {json.dumps(metric_data)}")


# Attach utility functions to logging module
logging.log_event = log_event
logging.log_metric = log_metric
