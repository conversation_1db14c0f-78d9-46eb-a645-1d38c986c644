2025-08-20 02:38:05 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:38:48 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:40:08 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:40:47 | ERROR    | 13544 | 23624 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:41:47 | ERROR    | 13544 | 21168 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 02:42:45 | ERROR    | 13544 | 21168 | app.py:6011 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:36:27 | ERROR    | 27996 | 1652 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:37:20 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:39:11 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:39:38 | ERROR    | 27996 | 23944 | app.py:6249 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:55:00 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:00 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:03 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:03 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:12 | ERROR    | 4960 | 32940 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:55:41 | ERROR    | 4960 | 32940 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:41 | ERROR    | 4960 | 32940 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:55:54 | ERROR    | 4960 | 32940 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 18:56:05 | ERROR    | 4960 | 34100 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
    return self._build_time_context(profile, recent_sessions, user_id)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
    last_active_str = profile.get('last_active', '')
                      ^^^^^^^^^^^
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:56:05 | ERROR    | 4960 | 34100 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:58:06 | ERROR    | 4960 | 34100 | app.py:887 | get_connection | Error during DB connection usage: 'sqlite3.Row' object has no attribute 'get'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 869, in get_connection
    yield conn
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3078, in get_time_context
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 3213, in _build_time_context
AttributeError: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:58:06 | ERROR    | 4960 | 34100 | app.py:3081 | get_time_context | Error getting time context for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'sqlite3.Row' object has no attribute 'get'
2025-08-20 18:58:12 | ERROR    | 4960 | 34100 | app.py:6625 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:02:11 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:02:42 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:03:29 | ERROR    | 21380 | 19712 | app.py:6635 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:23:25 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:23:59 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:29:36 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 19:31:02 | ERROR    | 37584 | 31396 | app.py:7057 | _humanize_and_finalize_response | NLTK 'punkt' data not found during humanization. Using basic splitting.
2025-08-20 20:03:53 | ERROR    | 30736 | 20400 | app.py:8747 | generate_personalized_response | CRITICAL Unhandled error during response generation for user 2f1c37bc-00ba-4e2e-ad38-5db6f523a45f: 'ChatManager' object has no attribute '_get_direct_fact_recall'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 8400, in generate_personalized_response
    prompt = self._construct_llm_prompt(
        user_message_text=user_message_text, user_id=user_id, user_profile=user_profile,
    ...<2 lines>...
        style_params=style_params, _chat_history=current_chat_history # Pass current history before user message
    )
  File "C:\Users\<USER>\Downloads\google-hack\app.py", line 7382, in _construct_llm_prompt
    direct_fact_recall = self._get_direct_fact_recall(user_id, user_message_text)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ChatManager' object has no attribute '_get_direct_fact_recall'
2025-08-20 20:09:52 | ERROR    | 8760 | 22892 | app.py:2428 | extract_facts_from_message | Error calling LLM for fact extraction: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 8
}
]
2025-08-20 20:09:53 | ERROR    | 8760 | 22892 | app.py:2439 | extract_facts_from_message | Error calling fallback LLM for fact extraction: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_input_token_count"
  quota_id: "GenerateContentInputTokensPerModelPerMinute-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-pro"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 7
}
]
2025-09-10 02:54:00 | CRITICAL | 25856 | 17016 | APP.PY:10541 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10529, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 02:54:50 | CRITICAL | 2576 | 21176 | APP.PY:10541 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10529, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:00:58 | ERROR    | 23788 | 21756 | APP.PY:7045 | __init__ | Failed to load sentiment model (cardiffnlp/twitter-roberta-base-sentiment-latest): [enforce fail at alloc_cpu.cpp:121] data. DefaultCPUAllocator: not enough memory: you tried to allocate 9437184 bytes.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 7040, in __init__
    self.sentiment_model = AutoModelForSequenceClassification.from_pretrained(sentiment_model_name)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\auto\auto_factory.py", line 564, in from_pretrained
    return model_class.from_pretrained(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\modeling_utils.py", line 3832, in from_pretrained
    model = cls(config, *model_args, **model_kwargs)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 1160, in __init__
    self.roberta = RobertaModel(config, add_pooling_layer=False)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 701, in __init__
    self.encoder = RobertaEncoder(config)
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 474, in __init__
    self.layer = nn.ModuleList([RobertaLayer(config) for _ in range(config.num_hidden_layers)])
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 474, in <listcomp>
    self.layer = nn.ModuleList([RobertaLayer(config) for _ in range(config.num_hidden_layers)])
                                ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 395, in __init__
    self.intermediate = RobertaIntermediate(config)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\transformers\models\roberta\modeling_roberta.py", line 355, in __init__
    self.dense = nn.Linear(config.hidden_size, config.intermediate_size)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\torch\nn\modules\linear.py", line 106, in __init__
    torch.empty((out_features, in_features), **factory_kwargs)
RuntimeError: [enforce fail at alloc_cpu.cpp:121] data. DefaultCPUAllocator: not enough memory: you tried to allocate 9437184 bytes.
2025-09-10 03:01:09 | CRITICAL | 23788 | 21756 | APP.PY:10760 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10748, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:12:46 | CRITICAL | 9644 | 20856 | APP.PY:10808 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\APP.PY", line 10796, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:42:26 | CRITICAL | 2388 | 5504 | app.py:11034 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\app.py", line 11022, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
2025-09-10 03:43:20 | CRITICAL | 18932 | 9468 | app.py:11034 | <module> | FATAL: Failed to create or launch Gradio interface: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\refact-vscode-main (2)\google-hack\app.py", line 11022, in <module>
    demo.launch(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\blocks.py", line 2371, in launch
    ) = http_server.start_server(
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python311\site-packages\gradio\http_server.py", line 154, in start_server
    raise OSError(
OSError: Cannot find empty port in range: 7861-7861. You can specify a different port by setting the GRADIO_SERVER_PORT environment variable or passing the `server_port` parameter to `launch()`.
