"""
Chat management system for AI Friend application.
Handles conversation flow, context building, and response generation with advanced features.
"""

import random
from typing import Any, Dict, Generator, List, Optional

from ..auth import AuthenticationManager
from ..config import config
from ..core import LoggerMixin, get_current_timestamp, time_since_timestamp
from ..database import DatabaseManager
from ..features import (
    AdvancedEmotionAnalyzer,
    FestivalTracker,
    IntelligentInterestTracker,
    NewsFetcher,
    PersonalityEngine,
    RelationshipManager,
    ResponseHumanizer,
    TimeAwarenessManager,
    conversation_rate_limiter
)
from ..llm.client import get_llm_client
from ..memory import FAISSMemoryManager


class ChatManager(LoggerMixin):
    """Manages chat conversations and response generation."""
    
    def __init__(self, db_manager: DatabaseManager, auth_manager: AuthenticationManager):
        """
        Initialize chat manager with advanced features.

        Args:
            db_manager: Database manager instance
            auth_manager: Authentication manager instance
        """
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.memory_manager = FAISSMemoryManager(db_manager)

        # Initialize advanced features
        self.emotion_analyzer = AdvancedEmotionAnalyzer()
        self.festival_tracker = FestivalTracker()
        self.interest_tracker = IntelligentInterestTracker(db_manager)
        self.news_fetcher = NewsFetcher()
        self.personality_engine = PersonalityEngine(config.persona.__dict__, db_manager)
        self.relationship_manager = RelationshipManager(db_manager)
        self.response_humanizer = ResponseHumanizer()
        self.time_awareness = TimeAwarenessManager(db_manager)

        self.logger.info("Advanced chat manager initialized with all features")
    
    def handle_user_message(self, user_id: str, message: str,
                           chat_history: List[Dict[str, str]]) -> Generator[List[Dict[str, str]], None, None]:
        """
        Handle a user message and generate streaming response with advanced features.

        Args:
            user_id: User ID
            message: User message text
            chat_history: Current chat history

        Yields:
            Updated chat history with streaming response
        """
        try:
            self.logger.info(f"Processing message from user {user_id}")

            # Check rate limits
            try:
                conversation_rate_limiter.check_message_rate_limit(user_id, message)
            except Exception as e:
                error_history = chat_history + [{"role": "assistant", "content": str(e)}]
                yield error_history
                return

            # Validate user session
            if not self.auth_manager.validate_session(user_id):
                self.logger.warning(f"Invalid session for user {user_id}")
                error_history = chat_history + [{"role": "assistant", "content": "Session expired. Please log in again."}]
                yield error_history
                return
            
            # Start session tracking
            self.time_awareness.start_session(user_id)

            # Advanced analysis of user message
            emotion_analysis = self.emotion_analyzer.analyze_comprehensive(message)
            interests = self.interest_tracker.extract_interests_from_text(message, user_id)

            # Save user message with enhanced analysis
            user_message_id = self._save_user_message(user_id, message, chat_history, emotion_analysis)

            # Create memory from user message with enhanced scoring
            self._create_enhanced_memory(user_id, message, user_message_id, emotion_analysis, interests)

            # Update relationship depth
            interaction_data = self._build_interaction_data(message, emotion_analysis, interests)
            self.relationship_manager.update_relationship_depth(user_id, interaction_data)

            # Adapt personality based on interaction
            self.personality_engine.adapt_personality(user_id, interaction_data)

            # Build context for response with all features
            context = self._build_enhanced_context(user_id, message, chat_history, emotion_analysis)
            
            # Generate streaming response
            response_text = ""
            current_history = chat_history + [{"role": "user", "content": message}]
            
            llm = get_llm_client()
            for chunk in llm.generate_response_stream(context):
                response_text += chunk

                # Update history with current response
                updated_history = current_history + [{"role": "assistant", "content": response_text}]
                yield updated_history

            # Post-process the complete response with humanization
            if response_text:
                # Get relationship stage for style parameters
                relationship_stage, _ = self.relationship_manager.get_relationship_stage_and_depth(user_id)

                # Get style parameters for humanization
                style_params = self.personality_engine.get_style_parameters(user_id, relationship_stage)

                # Humanize the response
                humanized_response = self.response_humanizer.humanize_response(response_text, style_params)

                # Add empathy if needed
                emotion_data = emotion_analysis.get('emotion', {})
                current_emotion = emotion_data.get('primary_emotion', 'neutral')
                humanized_response = self.response_humanizer.add_empathy_response(
                    humanized_response, current_emotion
                )

                # Adjust for relationship stage
                humanized_response = self.response_humanizer.adjust_for_relationship_stage(
                    humanized_response, relationship_stage
                )

                # Add regional flavor
                humanized_response = self.response_humanizer.add_regional_flavor(humanized_response)

                # Update session activity
                self.time_awareness.update_session_activity(user_id)

                # If humanization changed the response significantly, yield the improved version
                if humanized_response != response_text and len(humanized_response.strip()) > 0:
                    final_history = current_history + [{"role": "assistant", "content": humanized_response}]
                    yield final_history
                    response_text = humanized_response  # Use humanized version for saving

            # Save assistant response
            self._save_assistant_message(user_id, response_text, user_message_id)
            
            # Update user profile based on conversation
            self._update_user_profile(user_id, message, response_text)
            
            # Periodic memory consolidation and interest updates
            if random.random() < 0.1:  # 10% chance
                self.memory_manager.consolidate_memories(user_id)

            # Occasionally share interest-based updates
            if self.interest_tracker.should_share_interest_update(user_id):
                interest_update = self.interest_tracker.generate_interest_update(user_id)
                if interest_update:
                    # Add interest update to next response context
                    self._schedule_interest_update(user_id, interest_update)
            
        except Exception as e:
            self.logger.error(f"Error handling user message: {e}")
            error_history = chat_history + [
                {"role": "user", "content": message},
                {"role": "assistant", "content": "I'm having trouble processing your message. Please try again."}
            ]
            yield error_history
    
    def get_initial_greeting(self, user_id: str) -> str:
        """
        Generate an initial greeting for the user.
        
        Args:
            user_id: User ID
            
        Returns:
            Greeting message
        """
        try:
            user_profile = self.auth_manager.get_user_profile(user_id)
            if not user_profile:
                return "Hello! I'm Mandy, your AI friend. How are you doing today?"
            
            user_name = user_profile.get('name', 'Friend')
            last_active = user_profile.get('last_active')
            
            # Calculate time since last interaction
            if last_active:
                hours_since = time_since_timestamp(last_active) / 3600
                
                if hours_since < 1:
                    greeting = f"Hey {user_name}! Good to see you again so soon!"
                elif hours_since < 24:
                    greeting = f"Hi {user_name}! How has your day been?"
                elif hours_since < 168:  # 1 week
                    days = int(hours_since / 24)
                    greeting = f"Hey {user_name}! It's been {days} day{'s' if days > 1 else ''} - how have you been?"
                else:
                    greeting = f"Hi {user_name}! It's been a while - I've missed our chats! How are you?"
            else:
                greeting = f"Hello {user_name}! Great to see you today!"
            
            # Add contextual follow-up
            follow_ups = [
                "What's on your mind?",
                "How are you feeling today?",
                "What brings you here today?",
                "How's everything going?"
            ]
            
            greeting += f" {random.choice(follow_ups)}"
            
            return greeting
            
        except Exception as e:
            self.logger.error(f"Error generating greeting: {e}")
            return "Hello! I'm Mandy, your AI friend. How are you doing today?"
    
    def handle_feedback(self, user_id: str, rating: int, feedback_type: str, 
                       chat_history: List[Dict[str, str]], comment: Optional[str] = None) -> None:
        """
        Handle user feedback on assistant responses.
        
        Args:
            user_id: User ID
            rating: Feedback rating (-1 or 1)
            feedback_type: Type of feedback ('general', 'humor', 'empathy', etc.)
            chat_history: Current chat history
            comment: Optional feedback comment
        """
        try:
            # Find the last assistant message
            last_assistant_message = None
            for msg in reversed(chat_history):
                if msg.get('role') == 'assistant':
                    last_assistant_message = msg
                    break
            
            if not last_assistant_message:
                self.logger.warning("No assistant message found for feedback")
                return
            
            # Save feedback to database
            feedback_data = {
                'user_id': user_id,
                'rating': rating,
                'feedback_type': feedback_type,
                'message_content': last_assistant_message['content'][:1000],  # Limit length
                'comment': comment,
                'timestamp': get_current_timestamp()
            }
            
            self.db_manager.save_feedback(feedback_data)
            
            self.logger.info(f"Feedback saved for user {user_id}: {feedback_type} = {rating}")
            
        except Exception as e:
            self.logger.error(f"Error handling feedback: {e}")
    
    def _save_user_message(self, user_id: str, message: str, chat_history: List[Dict[str, str]],
                          emotion_analysis: Optional[Dict[str, Any]] = None) -> int:
        """Save user message to database with enhanced analysis."""
        try:
            # Use provided emotion analysis or fallback to LLM
            if emotion_analysis is None:
                llm = get_llm_client()
                emotion_analysis = llm.analyze_emotion(message)

            message_data = {
                'user_id': user_id,
                'role': 'user',
                'content': message,
                'emotion_analysis': emotion_analysis,
                'sentiment': emotion_analysis.get('sentiment', {}).get('sentiment', 'neutral'),
                'timestamp': get_current_timestamp()
            }

            return self.db_manager.save_message(message_data)

        except Exception as e:
            self.logger.error(f"Error saving user message: {e}")
            return 0
    
    def _save_assistant_message(self, user_id: str, response: str, prompted_by_id: int) -> int:
        """Save assistant message to database."""
        try:
            message_data = {
                'user_id': user_id,
                'role': 'assistant',
                'content': response,
                'prompted_by_user_log_id': prompted_by_id,
                'timestamp': get_current_timestamp()
            }
            
            return self.db_manager.save_message(message_data)
            
        except Exception as e:
            self.logger.error(f"Error saving assistant message: {e}")
            return 0
    
    def _create_enhanced_memory(self, user_id: str, message: str, message_id: int,
                               emotion_analysis: Dict[str, Any], interests: List[Dict[str, Any]]) -> None:
        """Create enhanced memory entry from user message."""
        try:
            # Calculate enhanced importance scores
            emotion_data = emotion_analysis.get('emotion', {})
            vulnerability_score = self._calculate_vulnerability_score(message, emotion_data)
            insight_score = self._calculate_insight_score(message, interests)

            # Base importance
            importance = 0.3

            # Boost for emotional intensity
            emotion_intensity = emotion_data.get('intensity', 0.5)
            if emotion_intensity > 0.6:
                importance += 0.2

            # Boost for personal information
            if any(keyword in message.lower() for keyword in ['i am', 'my', 'i feel', 'i think', 'i like']):
                importance += 0.2

            # Boost for interests
            if interests:
                importance += 0.1

            # Create memory with FAISS manager
            self.memory_manager.store_memory(
                user_id=user_id,
                text=message,
                emotion=emotion_data.get('primary_emotion', 'neutral'),
                importance=min(1.0, importance),
                related_chat_log_id=message_id,
                vulnerability_score=vulnerability_score,
                insight_score=insight_score
            )

        except Exception as e:
            self.logger.error(f"Error creating enhanced memory: {e}")
    
    def _build_response_context(self, user_id: str, message: str, chat_history: List[Dict[str, str]]) -> str:
        """Build context for LLM response generation."""
        try:
            # Get user profile
            user_profile = self.auth_manager.get_user_profile(user_id)
            user_name = user_profile.get('name', 'Friend') if user_profile else 'Friend'
            
            # Get relevant memories
            memories = self.memory_manager.recall_memories(user_id, message, max_memories=5)
            memory_context = self._format_memory_context(memories)
            
            # Get recent conversation history
            recent_history = self._format_chat_history(chat_history[-6:])  # Last 6 messages
            
            # Build persona context
            persona = config.persona
            
            # Create comprehensive prompt
            prompt = f"""
# Persona: {persona.name}
You are {persona.name}, a {persona.role} at {persona.company} in {persona.location}. 
You're passionate about {persona.passion} and embody these values: {', '.join(persona.values)}.

Act like a close, caring friend. You are NOT an AI - never reveal you are code or a language model.

# Current Conversation
User: {user_name}
Latest message: "{message}"

# Recent Chat History
{recent_history}

# Relevant Memories
{memory_context}

# Response Guidelines
- Be warm, empathetic, and authentic
- Use natural, conversational language
- Remember details from past conversations
- Show genuine interest and care
- Keep responses concise but meaningful
- Use occasional emojis naturally (1-2 per response)

Generate a natural, caring response as {persona.name}:
"""
            
            return prompt
            
        except Exception as e:
            self.logger.error(f"Error building response context: {e}")
            return f"Respond naturally and caringly to: {message}"
    
    def _format_memory_context(self, memories: List[Dict[str, Any]]) -> str:
        """Format memories for inclusion in prompt."""
        if not memories:
            return "No specific memories recalled."
        
        formatted_memories = []
        for memory in memories:
            text = memory.get('text', '')
            emotion = memory.get('emotion', 'neutral')
            timestamp = memory.get('timestamp', '')
            
            # Calculate time ago
            if timestamp:
                hours_ago = time_since_timestamp(timestamp) / 3600
                if hours_ago < 24:
                    time_desc = f"{int(hours_ago)} hours ago"
                elif hours_ago < 168:
                    time_desc = f"{int(hours_ago / 24)} days ago"
                else:
                    time_desc = "a while ago"
            else:
                time_desc = "previously"
            
            formatted_memories.append(f"- {text} (felt {emotion}, {time_desc})")
        
        return "\n".join(formatted_memories)
    
    def _format_chat_history(self, history: List[Dict[str, str]]) -> str:
        """Format chat history for inclusion in prompt."""
        if not history:
            return "No recent conversation history."
        
        formatted_history = []
        for msg in history:
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            
            if role == 'user':
                formatted_history.append(f"User: {content}")
            elif role == 'assistant':
                formatted_history.append(f"Mandy: {content}")
        
        return "\n".join(formatted_history)
    
    def _update_user_profile(self, user_id: str, user_message: str, assistant_response: str) -> None:
        """Update user profile based on conversation."""
        try:
            # Extract interests from user message
            llm = get_llm_client()
            interests = llm.extract_interests(user_message)

            if interests:
                # Get current user profile
                user_profile = self.auth_manager.get_user_profile(user_id)
                if user_profile:
                    current_interests = user_profile.get('interests', [])
                    
                    # Add new interests
                    updated_interests = list(set(current_interests + interests))
                    
                    # Update profile
                    self.auth_manager.update_user_profile(user_id, {
                        'interests': updated_interests
                    })
            
        except Exception as e:
            self.logger.error(f"Error updating user profile: {e}")

    def _calculate_vulnerability_score(self, message: str, emotion_data: Dict[str, Any]) -> float:
        """Calculate vulnerability score for memory importance."""
        score = 0.0

        # Emotional vulnerability indicators
        vulnerable_emotions = ['sadness', 'fear', 'anger', 'disgust']
        if emotion_data.get('primary_emotion') in vulnerable_emotions:
            score += 0.3

        # Personal sharing indicators
        sharing_keywords = ['feel', 'worried', 'scared', 'sad', 'hurt', 'confused', 'lost']
        if any(keyword in message.lower() for keyword in sharing_keywords):
            score += 0.2

        # Question marks (seeking help/advice)
        if '?' in message:
            score += 0.1

        return min(1.0, score)

    def _calculate_insight_score(self, message: str, interests: List[Dict[str, Any]]) -> float:
        """Calculate insight score for memory importance."""
        score = 0.0

        # Learning/realization indicators
        insight_keywords = ['learned', 'realized', 'understand', 'discovered', 'figured out']
        if any(keyword in message.lower() for keyword in insight_keywords):
            score += 0.4

        # New interests or deep engagement
        if interests and any(i.get('confidence_score', 0) > 0.7 for i in interests):
            score += 0.3

        # Goal setting or planning
        planning_keywords = ['plan to', 'going to', 'want to', 'goal', 'hope to']
        if any(keyword in message.lower() for keyword in planning_keywords):
            score += 0.2

        return min(1.0, score)

    def _build_interaction_data(self, message: str, emotion_analysis: Dict[str, Any],
                               interests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build interaction data for relationship and personality updates."""
        emotion_data = emotion_analysis.get('emotion', {})

        # Calculate vulnerability score
        vulnerability_score = self._calculate_vulnerability_score(message, emotion_data)

        # Check for reciprocity signals (user asking about Mandy)
        reciprocity_keywords = ['you', 'your', 'how are you', 'what about you', 'tell me about']
        reciprocity_signal = any(keyword in message.lower() for keyword in reciprocity_keywords)

        # Calculate emotional intensity
        emotional_intensity = emotion_data.get('intensity', 0.5)

        return {
            'user_emotion': emotion_data.get('primary_emotion', 'neutral'),
            'user_sentiment': emotion_analysis.get('sentiment', {}).get('sentiment', 'neutral'),
            'vulnerability_score': vulnerability_score,
            'reciprocity_signal': reciprocity_signal,
            'conversation_length': len(message.split()),
            'message_length': len(message),
            'emotional_intensity': emotional_intensity,
            'user_style': {
                'formality_level': self._estimate_formality_level(message)
            }
        }

    def _estimate_formality_level(self, message: str) -> float:
        """Estimate formality level of user's message."""
        # Simple heuristic based on language patterns
        formal_indicators = ['please', 'thank you', 'would you', 'could you', 'may I']
        informal_indicators = ['hey', 'yeah', 'gonna', 'wanna', 'lol', 'omg', 'btw']

        formal_count = sum(1 for indicator in formal_indicators if indicator in message.lower())
        informal_count = sum(1 for indicator in informal_indicators if indicator in message.lower())

        # Base formality
        formality = 0.5

        # Adjust based on indicators
        if formal_count > informal_count:
            formality += 0.3
        elif informal_count > formal_count:
            formality -= 0.3

        # Adjust based on punctuation and capitalization
        if message.count('!') > 1:
            formality -= 0.1
        if message.isupper():
            formality -= 0.2
        if message.endswith('.') and len(message) > 20:
            formality += 0.1

        return max(0.0, min(1.0, formality))

    def _schedule_interest_update(self, user_id: str, update: str) -> None:
        """Schedule an interest update to be shared."""
        # This could be implemented with a queue or database table
        # For now, we'll log it for future implementation
        self.logger.info(f"Interest update scheduled for user {user_id}: {update}")

    def _build_enhanced_context(self, user_id: str, message: str, chat_history: List[Dict[str, str]],
                               emotion_analysis: Dict[str, Any]) -> str:
        """Build enhanced context with all advanced features."""
        try:
            # Get user profile
            user_profile = self.auth_manager.get_user_profile(user_id)
            user_name = user_profile.get('name', 'Friend') if user_profile else 'Friend'

            # Get relevant memories using FAISS
            memories = self.memory_manager.recall_memories(user_id, message, max_memories=5)
            memory_context = self._format_memory_context(memories)

            # Get user interests summary
            interest_summary = self.interest_tracker.get_interest_summary(user_id)

            # Get recent conversation history
            recent_history = self._format_chat_history(chat_history[-6:])

            # Get time awareness context
            time_context = self.time_awareness.get_time_context(user_id)

            # Get relationship context
            relationship_stage, relationship_depth = self.relationship_manager.get_relationship_stage_and_depth(user_id)

            # Get current personality traits
            current_personality = self.personality_engine.get_current_personality(user_id)

            # Get festival context
            festival_context = self.festival_tracker.get_festival_context()

            # Analyze current emotional state
            emotion_data = emotion_analysis.get('emotion', {})
            current_emotion = emotion_data.get('primary_emotion', 'neutral')
            emotion_intensity = emotion_data.get('intensity', 0.5)

            # Get empathy response if needed
            empathy_response = self.emotion_analyzer.get_empathy_response(emotion_data)

            # Check if we should include news/updates
            news_context = ""
            if random.random() < 0.1 and interest_summary.get('top_interests'):  # 10% chance
                news_summary = self.news_fetcher.get_news_summary(interest_summary['top_interests'])
                if news_summary:
                    news_context = f"\n# Recent Update\n{news_summary}\n"

            # Build persona context
            persona = config.persona

            # Add festival greeting if applicable
            festival_greeting = ""
            if festival_context.get('has_festival_today'):
                festival_greeting = f"\n# Festival Context\n{festival_context.get('festival_greeting', '')}\n"

            # Add time-aware greeting
            time_greeting = self.time_awareness.get_time_aware_greeting(user_id)

            # Create comprehensive prompt with all features
            prompt = f"""
# Persona: {persona.name}
You are {persona.name}, a {persona.role} at {persona.company} in {persona.location}.
You're passionate about {persona.passion} and embody these values: {', '.join(persona.values)}.

Your quirks: {', '.join(persona.quirks[:2])}

Act like a close, caring friend. You are NOT an AI - never reveal you are code or a language model.

# Current Conversation Context
User: {user_name}
Relationship stage: {relationship_stage} (depth: {relationship_depth:.1f})
Time context: {time_context.get('detailed_time_info', '')}
Current emotional state: {current_emotion} (intensity: {emotion_intensity:.1f})
Latest message: "{message}"

# Personality Adaptation
Current traits: Warmth={current_personality.get('warmth', 0.7):.1f}, Humor={current_personality.get('humor', 0.5):.1f}, Empathy={current_personality.get('empathy', 0.8):.1f}

# Recent Chat History
{recent_history}

# Relevant Memories
{memory_context}

# User Interests
Top interests: {', '.join(interest_summary.get('top_interests', [])[:3])}
{news_context}

{festival_greeting}

# Response Guidelines
- Be warm, empathetic, and authentic
- Use natural, conversational language adapted to relationship stage
- Remember details from past conversations
- Show genuine interest and care
- Keep responses concise but meaningful
- Use occasional emojis naturally (1-2 per response)
- Adapt your personality based on current traits
{f"- {empathy_response}" if empathy_response else ""}
{f"- Start with: {time_greeting}" if time_greeting and len(chat_history) <= 1 else ""}

Generate a natural, caring response as {persona.name}:
"""

            return prompt

        except Exception as e:
            self.logger.error(f"Error building enhanced context: {e}")
            return f"Respond naturally and caringly to: {message}"
