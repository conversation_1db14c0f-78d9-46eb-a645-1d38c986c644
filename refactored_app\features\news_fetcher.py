"""
News Fetcher for AI Friend application.
Fetches relevant news and trending topics using web search.
"""

import random
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

import requests
from bs4 import BeautifulSoup

try:
    from googlesearch import search
except ImportError:
    search = None

try:
    from ..config import config
    from ..core import LoggerMixin
except ImportError:
    from config import config
    from core import LoggerMixin


class NewsFetcher(LoggerMixin):
    """Fetches news using Google Search with caching and basic filtering."""
    
    def __init__(self):
        """Initialize news fetcher."""
        self.cache: Dict[str, Tuple[datetime, List[str]]] = {}
        self.cache_duration = timedelta(minutes=30)  # Cache for 30 minutes
        
        # Setup session with headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36'
        })
        
        self.logger.info("News fetcher initialized")
    
    def fetch_news(self, query: str, num_results: int = 3) -> List[str]:
        """
        Fetch news headlines for a given query.
        
        Args:
            query: Search query for news
            num_results: Number of results to fetch
            
        Returns:
            List of news headlines
        """
        try:
            # Check cache first
            cache_key = f"{query}_{num_results}"
            if cache_key in self.cache:
                cached_time, cached_results = self.cache[cache_key]
                if datetime.now() - cached_time < self.cache_duration:
                    self.logger.debug(f"Returning cached results for query: {query}")
                    return cached_results
            
            # Fetch fresh results
            headlines = self._fetch_headlines(query, num_results)
            
            # Cache results
            self.cache[cache_key] = (datetime.now(), headlines)
            
            # Limit cache size
            if len(self.cache) > 50:
                # Remove oldest entries
                oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k][0])
                del self.cache[oldest_key]
            
            return headlines
            
        except Exception as e:
            self.logger.error(f"Error fetching news for query '{query}': {e}")
            return []
    
    def _fetch_headlines(self, query: str, num_results: int = 3) -> List[str]:
        """Internal method to perform search and extract titles."""
        if search is None:
            self.logger.warning("Google Search library not available. Skipping news fetch.")
            return []
        
        self.logger.info(f"Fetching news for query: '{query}' (Num results: {num_results})")
        
        try:
            # Perform Google search with news focus
            news_query = f"{query} news"
            results_iterator = search(
                news_query,
                num=num_results,
                lang='en',
                safe='on',
                pause=random.uniform(1.5, 3.0)
            )
            
            results_urls = list(results_iterator) if results_iterator else []
            time.sleep(0.5)  # Small delay after search
            
        except Exception as e:
            self.logger.error(f"Error during Google search for '{query}': {e}")
            if "HTTP Error 429" in str(e):
                self.logger.warning("Google Search rate limit hit. Consider longer pauses.")
            return []
        
        if not results_urls:
            self.logger.warning(f"No search results returned for query: '{query}'")
            return []
        
        # Extract headlines from URLs
        headlines = []
        max_headline_len = 100
        
        for url in results_urls:
            try:
                headline = self._extract_headline_from_url(url, max_headline_len)
                if headline:
                    headlines.append(headline)
                    
            except Exception as e:
                self.logger.warning(f"Error extracting headline from {url}: {e}")
                continue
        
        self.logger.info(f"Successfully extracted {len(headlines)} headlines for query: '{query}'")
        return headlines
    
    def _extract_headline_from_url(self, url: str, max_length: int = 100) -> Optional[str]:
        """Extract headline from a URL."""
        try:
            response = self.session.get(url, timeout=6, allow_redirects=True)
            response.raise_for_status()
            
            content_type = response.headers.get('Content-Type', '').lower()
            
            # Check if it's HTML content
            if 'html' in content_type:
                soup = BeautifulSoup(response.content, 'html.parser')
                title_tag = soup.find('title')
                
                if title_tag and title_tag.string:
                    raw_title = title_tag.string.strip()
                    
                    # Clean up common title suffixes
                    cleaned_title = re.sub(
                        r'\s*[-\|]\s*(?:News|Breaking News|.+?\.com|.+?\.in|Times of India|The Hindu|NDTV|.+? News).*$',
                        '', raw_title, flags=re.IGNORECASE
                    ).strip()
                    
                    headline = cleaned_title if cleaned_title else raw_title
                    return headline[:max_length]
            
            # Fallback if no title found
            return self._generate_fallback_headline(url, max_length)
            
        except requests.RequestException as e:
            self.logger.warning(f"Request error for {url}: {e}")
            return self._generate_fallback_headline(url, max_length)
        
        except Exception as e:
            self.logger.warning(f"Unexpected error extracting headline from {url}: {e}")
            return None
    
    def _generate_fallback_headline(self, url: str, max_length: int) -> str:
        """Generate fallback headline from URL."""
        try:
            domain = url.split('/')[2].replace('www.', '')
            last_part = url.split('/')[-1].replace('-', ' ').replace('_', ' ')
            
            # Try to make fallback more readable
            fallback_text = last_part if last_part and '.' not in last_part else domain
            return f"{fallback_text[:max_length-15]}... ({domain})"
            
        except IndexError:
            return f"Link: {url[:max_length]}"
    
    def fetch_trending_topics(self, categories: List[str] = None) -> Dict[str, List[str]]:
        """
        Fetch trending topics for specified categories.
        
        Args:
            categories: List of categories to fetch trends for
            
        Returns:
            Dictionary mapping categories to trending topics
        """
        if categories is None:
            categories = ['technology', 'entertainment', 'sports', 'business']
        
        trending_topics = {}
        
        for category in categories:
            try:
                # Fetch news for category
                headlines = self.fetch_news(f"{category} trending", num_results=5)
                
                # Extract topics from headlines
                topics = self._extract_topics_from_headlines(headlines)
                trending_topics[category] = topics
                
                # Small delay between category requests
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error fetching trends for category '{category}': {e}")
                trending_topics[category] = []
        
        return trending_topics
    
    def _extract_topics_from_headlines(self, headlines: List[str]) -> List[str]:
        """Extract key topics from headlines."""
        topics = []
        
        for headline in headlines:
            # Simple topic extraction - get meaningful words
            words = re.findall(r'\b[A-Z][a-z]+\b', headline)  # Capitalized words
            
            # Filter out common words
            stop_words = {'The', 'And', 'For', 'With', 'From', 'News', 'Breaking', 'Latest'}
            meaningful_words = [word for word in words if word not in stop_words and len(word) > 3]
            
            topics.extend(meaningful_words[:3])  # Take first 3 meaningful words
        
        # Remove duplicates and return
        return list(dict.fromkeys(topics))
    
    def get_news_summary(self, user_interests: List[str]) -> Optional[str]:
        """
        Get a news summary relevant to user interests.
        
        Args:
            user_interests: List of user's interests
            
        Returns:
            News summary string or None
        """
        try:
            if not user_interests:
                return None
            
            # Select a random interest to fetch news for
            selected_interest = random.choice(user_interests)
            
            # Fetch news for the interest
            headlines = self.fetch_news(selected_interest, num_results=2)
            
            if headlines:
                # Create a summary
                if len(headlines) == 1:
                    return f"Saw some {selected_interest} news: {headlines[0]}"
                else:
                    return f"Some {selected_interest} updates: {headlines[0]} Also, {headlines[1]}"
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error generating news summary: {e}")
            return None
    
    def search_specific_news(self, topic: str, location: str = "India") -> List[str]:
        """
        Search for specific news about a topic in a location.
        
        Args:
            topic: News topic to search for
            location: Location to focus search on
            
        Returns:
            List of relevant news headlines
        """
        try:
            query = f"{topic} {location} news"
            return self.fetch_news(query, num_results=3)
            
        except Exception as e:
            self.logger.error(f"Error searching specific news for '{topic}' in '{location}': {e}")
            return []
    
    def clear_cache(self) -> None:
        """Clear the news cache."""
        self.cache.clear()
        self.logger.info("News cache cleared")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        return {
            'total_entries': len(self.cache),
            'expired_entries': sum(
                1 for cached_time, _ in self.cache.values()
                if datetime.now() - cached_time > self.cache_duration
            )
        }
