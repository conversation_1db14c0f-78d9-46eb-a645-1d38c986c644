"""
LLM client for AI Friend application.
Handles communication with Google Gemini API.
"""

import json
import time
from typing import Any, Dict, Generator, List, Optional

import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential

try:
    from ..config import config
    from ..core import LL<PERSON>rro<PERSON>, LoggerMixin, clean_json_response
except ImportError:  # Support top-level package import in tests
    from config import config
    from core import LLMError, LoggerMixin, clean_json_response

_llm_singleton: Optional["LLMClient"] = None

def get_llm_client() -> "LLMClient":
    global _llm_singleton
    if _llm_singleton is None:
        _llm_singleton = LLMClient()
    return _llm_singleton


class LLMClient(LoggerMixin):
    """Client for interacting with Google Gemini API."""
    
    def __init__(self):
        """Initialize LLM client."""
        self._configure_api()
        self.logger.info("LLM client initialized")
    
    def _configure_api(self) -> None:
        """Configure the Gemini API."""
        try:
            genai.configure(api_key=config.llm.api_key)
            self.logger.info("Gemini API configured successfully")
        except Exception as e:
            self.logger.error(f"Failed to configure Gemini API: {e}")
            raise LLMError(f"Failed to configure Gemini API: {e}")
    
    @retry(wait=wait_exponential(multiplier=1, min=4, max=10), stop=stop_after_attempt(3), reraise=True)
    def generate_response(self, prompt: str, model_name: Optional[str] = None) -> str:
        """
        Generate a response from the LLM.
        
        Args:
            prompt: Input prompt
            model_name: Optional model name override
            
        Returns:
            Generated response text
        """
        try:
            model_name = model_name or config.llm.default_model
            model = genai.GenerativeModel(model_name)
            
            self.logger.debug(f"Generating response with model {model_name}")
            
            response = model.generate_content(prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                self.logger.warning("Empty response from LLM")
                return "I'm having trouble responding right now. Could you try again?"
                
        except Exception as e:
            self.logger.error(f"Error generating LLM response: {e}")
            # When wrapped with tenacity, preserve the original exception type after retries
            # by re-raising the original if already an LLMError, else wrap once.
            if isinstance(e, LLMError):
                raise
            raise LLMError(f"Failed to generate response: {e}")

    @retry(wait=wait_exponential(multiplier=1, min=4, max=10), stop=stop_after_attempt(3), reraise=True)
    def generate_response_stream(self, prompt: str, model_name: Optional[str] = None) -> Generator[str, None, None]:
        """
        Generate a streaming response from the LLM.
        
        Args:
            prompt: Input prompt
            model_name: Optional model name override
            
        Yields:
            Response text chunks
        """
        try:
            model_name = model_name or config.llm.default_model
            model = genai.GenerativeModel(model_name)
            
            self.logger.debug(f"Generating streaming response with model {model_name}")
            
            response = model.generate_content(prompt, stream=True)
            
            for chunk in response:
                if chunk.text:
                    # Add artificial delay for more natural typing effect
                    time.sleep(config.llm.response_delay_base)
                    yield chunk.text
                    
        except Exception as e:
            self.logger.error(f"Error generating streaming LLM response: {e}")
            yield "I'm having trouble responding right now. Could you try again?"
    
    def generate_json_response(self, prompt: str, model_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a JSON response from the LLM.
        
        Args:
            prompt: Input prompt expecting JSON response
            model_name: Optional model name override
            
        Returns:
            Parsed JSON response
        """
        try:
            response_text = self.generate_response(prompt, model_name)
            
            # Clean and parse JSON
            cleaned_json = clean_json_response(response_text)
            
            try:
                return json.loads(cleaned_json)
            except json.JSONDecodeError as e:
                self.logger.warning(f"Failed to parse JSON response: {e}")
                self.logger.debug(f"Raw response: {response_text}")
                return {"error": "Failed to parse JSON response"}
                
        except Exception as e:
            self.logger.error(f"Error generating JSON response: {e}")
            return {"error": str(e)}
    
    def summarize_conversation(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Summarize a conversation using the LLM.
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            
        Returns:
            Summary dictionary
        """
        try:
            # Build conversation text
            conversation_text = ""
            for msg in messages[-10:]:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                conversation_text += f"{role.title()}: {content}\n"
            
            # Create summarization prompt
            prompt = f"""
            Analyze this conversation and provide a JSON summary:

            Conversation:
            {conversation_text}

            Provide a JSON response with:
            - "summary": Brief summary of main points
            - "topics": List of main topics discussed
            - "emotions": List of emotions expressed by user
            - "key_insights": Important facts or revelations
            - "user_interests": Any interests mentioned
            - "vulnerability_score": 0.0-1.0 how much user opened up

            Respond with only valid JSON.
            """
            
            return self.generate_json_response(prompt, config.llm.summarization_model)
            
        except Exception as e:
            self.logger.error(f"Error summarizing conversation: {e}")
            return {
                "summary": "Conversation summary unavailable",
                "topics": [],
                "emotions": [],
                "key_insights": [],
                "user_interests": [],
                "vulnerability_score": 0.0
            }
    
    def analyze_emotion(self, text: str) -> Dict[str, Any]:
        """
        Analyze emotion in text using the LLM.
        
        Args:
            text: Text to analyze
            
        Returns:
            Emotion analysis dictionary
        """
        try:
            prompt = f"""
            Analyze the emotion in this text and respond with JSON:

            Text: "{text}"

            Provide:
            - "primary_emotion": Main emotion (happy, sad, angry, fearful, surprised, disgusted, neutral)
            - "intensity": 0.0-1.0 intensity score
            - "sentiment": positive, negative, or neutral
            - "confidence": 0.0-1.0 confidence in analysis

            Respond with only valid JSON.
            """
            
            result = self.generate_json_response(prompt)
            
            # Validate and set defaults
            return {
                "primary_emotion": result.get("primary_emotion", "neutral"),
                "intensity": max(0.0, min(1.0, result.get("intensity", 0.5))),
                "sentiment": result.get("sentiment", "neutral"),
                "confidence": max(0.0, min(1.0, result.get("confidence", 0.5)))
            }
            
        except Exception as e:
            self.logger.exception("Error analyzing emotion")
            return {
                "primary_emotion": "neutral",
                "intensity": 0.5,
                "sentiment": "neutral",
                "confidence": 0.0
            }
    
    def extract_interests(self, text: str) -> List[str]:
        """
        Extract user interests from text.
        
        Args:
            text: Text to analyze for interests
            
        Returns:
            List of extracted interests
        """
        try:
            prompt = f"""
            Extract user interests from this text. Return as JSON array of strings.

            Text: "{text}"

            Look for mentions of:
            - Hobbies and activities
            - Topics they enjoy
            - Things they're passionate about
            - Skills they're learning
            - Entertainment preferences

            Return only a JSON array like: ["interest1", "interest2", ...]
            """
            
            result = self.generate_json_response(prompt)
            
            if isinstance(result, list):
                return [str(interest) for interest in result if isinstance(interest, str)]
            elif isinstance(result, dict) and "interests" in result:
                interests = result["interests"]
                if isinstance(interests, list):
                    return [str(interest) for interest in interests if isinstance(interest, str)]
            
            return []
            
        except Exception as e:
            self.logger.exception("Error extracting interests")
            return []

    def health_check(self) -> bool:
        """
        Perform a health check on the LLM service.
        
        Returns:
            True if service is healthy
        """
        try:
            response = self.generate_response("Hello, respond with 'OK' if you can hear me.")
            return "ok" in response.lower()
        except Exception as e:
            self.logger.exception("LLM health check failed")
            return False


