"""
Custom exceptions for the AI Friend application.
Provides consistent error handling across the application.
"""


class AIFriendException(Exception):
    """Base exception for AI Friend application."""
    pass


class ConfigurationError(AIFriendException):
    """Raised when there's a configuration error."""
    pass


class DatabaseError(AIFriendException):
    """Raised when there's a database-related error."""
    pass


class AuthenticationError(AIFriendException):
    """Raised when authentication fails."""
    pass


class ValidationError(AIFriendException):
    """Raised when input validation fails."""
    pass


class MemoryError(AIFriendException):
    """Raised when there's a memory system error."""
    pass


class LLMError(AIFriendException):
    """Raised when there's an LLM-related error."""
    pass


class RateLimitError(AIFriendException):
    """Raised when rate limit is exceeded."""
    pass


class SecurityError(AIFriendException):
    """Raised when there's a security-related error."""
    pass
