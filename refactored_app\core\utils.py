"""
Utility functions for the AI Friend application.
Common utilities used across different modules.
"""

import json
import re
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

import numpy as np


def get_current_timestamp() -> str:
    """Get current timestamp in ISO format with UTC timezone."""
    return datetime.now(timezone.utc).isoformat()


def time_since_timestamp(timestamp_iso: Optional[str]) -> float:
    """
    Calculate time difference in seconds from ISO timestamp string.
    
    Args:
        timestamp_iso: ISO format timestamp string
        
    Returns:
        Time difference in seconds, or infinity if timestamp is invalid
    """
    if not timestamp_iso:
        return float('inf')
    
    try:
        past_time = datetime.fromisoformat(timestamp_iso.replace('Z', '+00:00'))
        if past_time.tzinfo is None:
            past_time = past_time.replace(tzinfo=timezone.utc)
        
        now = datetime.now(timezone.utc)
        return max(0.0, (now - past_time).total_seconds())
    
    except (Value<PERSON>rror, TypeError):
        return float('inf')


def generate_uuid() -> str:
    """Generate a new UUID string."""
    return str(uuid.uuid4())


def clean_json_response(text: str) -> str:
    """
    Extract and validate JSON from text that may contain markdown formatting.
    
    Args:
        text: Text potentially containing JSON
        
    Returns:
        Cleaned JSON string or original text if no valid JSON found
    """
    if not isinstance(text, str):
        return ""
    
    # Remove markdown code blocks
    cleaned = re.sub(r'^```(?:json|JSON)?\s*\n?', '', text.strip(), flags=re.MULTILINE)
    cleaned = re.sub(r'\n?\s*```$', '', cleaned, flags=re.MULTILINE)
    
    # Try to find JSON object/array
    match = re.search(r'^\s*(\{.*\}|\[.*\])\s*$', cleaned, re.DOTALL)
    if match:
        potential_json = match.group(1).strip()
        try:
            json.loads(potential_json)  # Validate
            return potential_json
        except json.JSONDecodeError:
            pass
    
    return cleaned.strip()


def safe_json_loads(text: str, default: Any = None) -> Any:
    """
    Safely load JSON with fallback to default value.
    
    Args:
        text: JSON string to parse
        default: Default value if parsing fails
        
    Returns:
        Parsed JSON or default value
    """
    try:
        return json.loads(text)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    Safely dump object to JSON with fallback.
    
    Args:
        obj: Object to serialize
        default: Default JSON string if serialization fails
        
    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(obj)
    except (TypeError, ValueError):
        return default


def normalize_embedding(embedding: np.ndarray) -> np.ndarray:
    """
    Normalize an embedding vector to unit length.
    
    Args:
        embedding: Input embedding vector
        
    Returns:
        Normalized embedding vector
    """
    if not isinstance(embedding, np.ndarray):
        raise ValueError("Embedding must be a numpy array")
    
    embedding = embedding.astype(np.float32)
    norm = np.linalg.norm(embedding)
    
    if norm > 0:
        return embedding / norm
    else:
        return embedding


def extract_keywords(text: str, min_length: int = 3, max_keywords: int = 10) -> List[str]:
    """
    Extract meaningful keywords from text.
    
    Args:
        text: Input text
        min_length: Minimum keyword length
        max_keywords: Maximum number of keywords to return
        
    Returns:
        List of extracted keywords
    """
    # Common stop words
    stop_words = {
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
        'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
        'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 
        'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 
        'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
    }
    
    # Extract words and filter
    words = re.findall(r'\b\w+\b', text.lower())
    keywords = [
        word for word in words 
        if word not in stop_words and len(word) >= min_length
    ]
    
    # Sort by length (longer words are often more specific)
    keywords.sort(key=len, reverse=True)
    
    return keywords[:max_keywords]


def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_username(username: str) -> Tuple[bool, str]:
    """
    Validate username format.
    
    Args:
        username: Username to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    if not username:
        return False, "Username cannot be empty"
    
    if len(username) < 3:
        return False, "Username must be at least 3 characters"
    
    if len(username) > 30:
        return False, "Username must be less than 30 characters"
    
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return False, "Username can only contain letters, numbers, underscore, and hyphen"
    
    return True, ""


def sanitize_text(text: str, max_length: int = 10000) -> str:
    """
    Sanitize text input by removing control characters.
    
    Args:
        text: Input text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
    """
    if not isinstance(text, str):
        return ""
    
    # Remove control characters except newlines and tabs
    sanitized = ''.join(
        c for c in text 
        if c in '\n\t' or (ord(c) >= 32 and ord(c) != 127)
    )
    
    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized.strip()


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split a list into chunks of specified size."""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float with default fallback."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """Safely convert value to int with default fallback."""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default
